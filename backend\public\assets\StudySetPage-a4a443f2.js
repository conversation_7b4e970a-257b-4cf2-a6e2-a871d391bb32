import{r as p,j as e,c as te,a as J,b as V,B as _,d as re,u as se}from"./index-fc7af858.js";import{u as K}from"./studyStore-f63a62e1.js";import{u as ae}from"./documentStore-d2d0d4ac.js";const W=({selectedDocuments:s,onSelectionChange:y,maxSelection:i=5})=>{const{documents:g,fetchDocuments:d,isLoading:u}=ae(),[n,E]=p.useState("");p.useEffect(()=>{g.length===0&&d()},[g.length,d]);const S=g.filter(o=>o.is_processed&&o.filename.toLowerCase().includes(n.toLowerCase())),j=o=>{s.includes(o)?y(s.filter(v=>v!==o)):s.length<i&&y([...s,o])},F=()=>g.filter(o=>s.includes(o.id));return u?e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsx("div",{className:"text-gray-400",children:"Loading documents..."})}):g.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"text-gray-400 mb-4",children:"No documents found"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Upload some documents first to generate study materials."})]}):e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{children:e.jsx("input",{type:"text",placeholder:"Search documents...",value:n,onChange:o=>E(o.target.value),className:"w-full px-3 py-2 bg-background-secondary border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500"})}),s.length>0&&e.jsxs("div",{className:"bg-primary-500/10 border border-primary-500/30 rounded-lg p-3",children:[e.jsxs("div",{className:"text-sm text-primary-400 mb-2",children:["Selected ",s.length," of ",i," documents:"]}),e.jsx("div",{className:"space-y-1",children:F().map(o=>e.jsxs("div",{className:"text-sm text-gray-300 flex items-center justify-between",children:[e.jsx("span",{className:"truncate",children:o.filename}),e.jsx("button",{onClick:()=>j(o.id),className:"text-red-400 hover:text-red-300 ml-2",children:"✕"})]},o.id))})]}),e.jsx("div",{className:"max-h-64 overflow-y-auto space-y-2",children:S.map(o=>{const w=s.includes(o.id),v=!w&&s.length<i;return e.jsx("div",{className:`
                p-3 rounded-lg border cursor-pointer transition-all
                ${w?"bg-primary-500/20 border-primary-500":v?"bg-background-secondary border-gray-600 hover:border-gray-500":"bg-gray-800 border-gray-700 opacity-50 cursor-not-allowed"}
              `,onClick:()=>v||w?j(o.id):null,children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex-1 min-w-0",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-lg",children:o.file_type==="pdf"?"📄":o.file_type==="docx"?"📝":o.file_type==="txt"?"📃":"📊"}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("p",{className:"text-white font-medium truncate",children:o.filename}),e.jsxs("p",{className:"text-sm text-gray-400",children:[o.file_type.toUpperCase()," • ",Math.round(o.file_size/1024)," KB"]})]})]})}),e.jsx("div",{className:`
                  w-5 h-5 rounded border-2 flex items-center justify-center
                  ${w?"bg-primary-500 border-primary-500":"border-gray-500"}
                `,children:w&&e.jsx("svg",{className:"w-3 h-3 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]})},o.id)})}),S.length===0&&n&&e.jsx("div",{className:"text-center py-4 text-gray-400",children:"No documents match your search."})]})};var l;(function(s){s.EASY="easy",s.MEDIUM="medium",s.HARD="hard",s.COLLEGE="college",s.GRADUATE="graduate",s.PHD="phd"})(l||(l={}));var $;(function(s){s.SHORT="short",s.MEDIUM="medium",s.LONG="long"})($||($={}));const Y=s=>({[l.EASY]:"Easy",[l.MEDIUM]:"Medium",[l.HARD]:"Hard",[l.COLLEGE]:"College",[l.GRADUATE]:"Graduate",[l.PHD]:"PhD"})[s],X=s=>({[l.EASY]:1,[l.MEDIUM]:3,[l.HARD]:4,[l.COLLEGE]:5,[l.GRADUATE]:6,[l.PHD]:7})[s],Z=s=>{switch(s){case 1:return l.EASY;case 2:return l.EASY;case 3:return l.MEDIUM;case 4:return l.HARD;case 5:return l.COLLEGE;case 6:return l.GRADUATE;case 7:return l.PHD;default:return l.MEDIUM}},ne=[{value:l.EASY,label:"Easy",description:"Basic facts and definitions"},{value:l.MEDIUM,label:"Medium",description:"Moderate understanding required"},{value:l.HARD,label:"Hard",description:"Deep analysis and critical thinking"},{value:l.COLLEGE,label:"College",description:"Undergraduate level complexity"},{value:l.GRADUATE,label:"Graduate",description:"Advanced graduate study"},{value:l.PHD,label:"PhD",description:"Research-level expertise"}],ie=s=>{switch(s){case l.EASY:return"bg-green-100 text-green-800 border-green-200 hover:bg-green-200";case l.MEDIUM:return"bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200";case l.HARD:return"bg-orange-100 text-orange-800 border-orange-200 hover:bg-orange-200";case l.COLLEGE:return"bg-purple-100 text-purple-800 border-purple-200 hover:bg-purple-200";case l.GRADUATE:return"bg-red-100 text-red-800 border-red-200 hover:bg-red-200";case l.PHD:return"bg-gray-100 text-gray-800 border-gray-200 hover:bg-gray-200";default:return"bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200"}},le=s=>{switch(s){case l.EASY:return"bg-green-500 text-white border-green-500";case l.MEDIUM:return"bg-blue-500 text-white border-blue-500";case l.HARD:return"bg-orange-500 text-white border-orange-500";case l.COLLEGE:return"bg-purple-500 text-white border-purple-500";case l.GRADUATE:return"bg-red-500 text-white border-red-500";case l.PHD:return"bg-gray-700 text-white border-gray-700";default:return"bg-blue-500 text-white border-blue-500"}},oe=({value:s,onChange:y,className:i="",disabled:g=!1,label:d="Difficulty Level"})=>e.jsxs("div",{className:`space-y-3 ${i}`,children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:d}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-2",children:ne.map(u=>{const n=s===u.value,E=n?le(u.value):ie(u.value);return e.jsxs("button",{type:"button",onClick:()=>!g&&y(u.value),disabled:g,className:`
                relative p-3 rounded-lg border-2 text-sm font-medium transition-all duration-200
                ${E}
                ${g?"opacity-50 cursor-not-allowed":"cursor-pointer"}
                ${n?"ring-2 ring-offset-2 ring-blue-500":""}
                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
              `,title:u.description,children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"font-semibold",children:u.label}),e.jsx("div",{className:`text-xs mt-1 ${n?"text-white/90":"opacity-75"}`,children:u.description})]}),n&&e.jsx("div",{className:"absolute top-1 right-1",children:e.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]},u.value)})}),e.jsx("p",{className:"text-xs text-gray-500",children:"Select the appropriate difficulty level for your flashcards. This affects the complexity of questions and answers generated."})]}),ce=[{value:$.SHORT,label:"Short",description:"Concise answers (1-2 sentences)",icon:"📝"},{value:$.MEDIUM,label:"Medium",description:"Balanced detail (2-3 sentences)",icon:"📄"},{value:$.LONG,label:"Long",description:"Comprehensive answers (3-5 sentences)",icon:"📋"}],de=s=>{switch(s){case $.SHORT:return"bg-emerald-100 text-emerald-800 border-emerald-200 hover:bg-emerald-200";case $.MEDIUM:return"bg-amber-100 text-amber-800 border-amber-200 hover:bg-amber-200";case $.LONG:return"bg-indigo-100 text-indigo-800 border-indigo-200 hover:bg-indigo-200";default:return"bg-amber-100 text-amber-800 border-amber-200 hover:bg-amber-200"}},ue=s=>{switch(s){case $.SHORT:return"bg-emerald-500 text-white border-emerald-500";case $.MEDIUM:return"bg-amber-500 text-white border-amber-500";case $.LONG:return"bg-indigo-500 text-white border-indigo-500";default:return"bg-amber-500 text-white border-amber-500"}},me=({value:s,onChange:y,className:i="",disabled:g=!1,label:d="Content Length"})=>e.jsxs("div",{className:`space-y-3 ${i}`,children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:d}),e.jsx("div",{className:"grid grid-cols-3 gap-3",children:ce.map(u=>{const n=s===u.value,E=n?ue(u.value):de(u.value);return e.jsxs("button",{type:"button",onClick:()=>!g&&y(u.value),disabled:g,className:`
                relative p-4 rounded-lg border-2 text-sm font-medium transition-all duration-200
                ${E}
                ${g?"opacity-50 cursor-not-allowed":"cursor-pointer"}
                ${n?"ring-2 ring-offset-2 ring-blue-500":""}
                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
              `,title:u.description,children:[e.jsxs("div",{className:"text-center space-y-2",children:[e.jsx("div",{className:"text-2xl",children:u.icon}),e.jsx("div",{className:"font-semibold",children:u.label}),e.jsx("div",{className:`text-xs ${n?"text-white/90":"opacity-75"}`,children:u.description})]}),n&&e.jsx("div",{className:"absolute top-2 right-2",children:e.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]},u.value)})}),e.jsx("p",{className:"text-xs text-gray-500",children:"Choose how detailed you want the flashcard answers to be. This affects the length and depth of explanations."})]}),he=te(s=>({isGenerating:!1,generationProgress:"",lastGenerated:null,generateFlashcards:async y=>{s({isGenerating:!0,generationProgress:"Preparing documents..."});try{const i=localStorage.getItem("auth_token");s({generationProgress:"Generating flashcards with AI..."});const g=await fetch("/api/ai/generate-flashcards",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${i}`},body:JSON.stringify(y)});if(!g.ok){const u=await g.json();throw new Error(u.error||"Generation failed")}const d=await g.json();if(d.success)return s({lastGenerated:{studySet:d.data.studySet,content:d.data.flashcards,type:"flashcards"},isGenerating:!1,generationProgress:""}),{studySet:d.data.studySet,flashcards:d.data.flashcards,creditsRemaining:d.data.creditsRemaining};throw new Error(d.error)}catch(i){throw s({isGenerating:!1,generationProgress:""}),i}},generateQuiz:async y=>{s({isGenerating:!0,generationProgress:"Preparing documents..."});try{const i=localStorage.getItem("auth_token");s({generationProgress:"Generating quiz questions with AI..."});const g=await fetch("/api/ai/generate-quiz",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${i}`},body:JSON.stringify(y)});if(!g.ok){const u=await g.json();throw new Error(u.error||"Generation failed")}const d=await g.json();if(d.success)return s({lastGenerated:{studySet:d.data.studySet,content:d.data.questions,type:"quiz"},isGenerating:!1,generationProgress:""}),{studySet:d.data.studySet,questions:d.data.questions,creditsRemaining:d.data.creditsRemaining};throw new Error(d.error)}catch(i){throw s({isGenerating:!1,generationProgress:""}),i}},generateMoreFlashcards:async y=>{s({isGenerating:!0,generationProgress:"Preparing documents..."});try{const i=localStorage.getItem("auth_token");s({generationProgress:"Generating additional flashcards with AI..."});const g=await fetch("/api/ai/generate-more-flashcards",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${i}`},body:JSON.stringify(y)});if(!g.ok){const u=await g.json();throw new Error(u.error||"Generation failed")}const d=await g.json();if(d.success)return s({isGenerating:!1,generationProgress:""}),{flashcards:d.data.flashcards,creditsRemaining:d.data.creditsRemaining};throw new Error(d.error)}catch(i){throw s({isGenerating:!1,generationProgress:""}),i}},clearLastGenerated:()=>{s({lastGenerated:null})}})),xe=({studySetId:s,flashcards:y,onFlashcardAdded:i,onFlashcardUpdated:g,onFlashcardDeleted:d,onFlashcardsGenerated:u})=>{const{alert:n,confirm:E}=J(),{user:S}=V(),{generateMoreFlashcards:j}=he(),[F,o]=p.useState(!1),[w,v]=p.useState([]),[T,A]=p.useState(10),[G,N]=p.useState(""),[M,R]=p.useState(l.MEDIUM),[Q,h]=p.useState($.MEDIUM),[k,O]=p.useState(!1),[P,c]=p.useState(!1),[b,L]=p.useState({front:"",back:"",difficulty_level:3}),[z,U]=p.useState(null),[D,I]=p.useState({front:"",back:"",difficulty_level:3}),q=async()=>{if(!b.front.trim()||!b.back.trim()){await n({title:"Validation Error",message:"Both front and back content are required.",variant:"error"});return}try{const r=await fetch(`/api/flashcards/study-set/${s}`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`},body:JSON.stringify({front:b.front.trim(),back:b.back.trim(),difficulty_level:b.difficulty_level,is_ai_generated:!1})});if(!r.ok)throw new Error("Failed to create flashcard");const m=await r.json();i(m.data),L({front:"",back:"",difficulty_level:3}),c(!1),await n({title:"Success",message:"Flashcard added successfully!",variant:"success"})}catch(r){await n({title:"Error",message:r.message||"Failed to add flashcard",variant:"error"})}},H=r=>{U(r),I({front:r.front,back:r.back,difficulty_level:typeof r.difficulty_level=="string"?X(r.difficulty_level):r.difficulty_level||3})},B=async()=>{if(z){if(!D.front.trim()||!D.back.trim()){await n({title:"Validation Error",message:"Both front and back content are required.",variant:"error"});return}try{const r=await fetch(`/api/flashcards/${z.id}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`},body:JSON.stringify({front:D.front.trim(),back:D.back.trim(),difficulty_level:D.difficulty_level})});if(!r.ok)throw new Error("Failed to update flashcard");const m=await r.json();g(m.data),U(null),I({front:"",back:"",difficulty_level:3}),await n({title:"Success",message:"Flashcard updated successfully!",variant:"success"})}catch(r){await n({title:"Error",message:r.message||"Failed to update flashcard",variant:"error"})}}},x=()=>{U(null),I({front:"",back:"",difficulty_level:3})},f=async r=>{if(await E({title:"Delete Flashcard",message:`Are you sure you want to delete this flashcard?

Front: ${r.front.substring(0,50)}${r.front.length>50?"...":""}`,variant:"danger",confirmText:"Delete",cancelText:"Cancel"}))try{if(!(await fetch(`/api/flashcards/${r.id}`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")}`}})).ok)throw new Error("Failed to delete flashcard");d(r.id),await n({title:"Success",message:"Flashcard deleted successfully!",variant:"success"})}catch(C){await n({title:"Error",message:C.message||"Failed to delete flashcard",variant:"error"})}},t=()=>T*2,a=async()=>{if(w.length===0){await n({title:"No Documents Selected",message:"Please select at least one document to generate flashcards from.",variant:"warning"});return}const r=t();if(S&&S.credits_remaining<r){await n({title:"Insufficient Credits",message:`You need ${r} credits to generate ${T} flashcards, but you only have ${S.credits_remaining} credits remaining.`,variant:"error"});return}if(await E({title:"Generate Flashcards",message:`Generate ${T} flashcards from ${w.length} document(s)?

This will cost ${r} credits.`,confirmText:"Generate",cancelText:"Cancel"})){O(!0);try{const C=await j({studySetId:s,documentIds:w,count:T,customPrompt:G.trim()||void 0,difficultyLevel:M,contentLength:Q});u(C.flashcards),S&&V.getState().updateUser({credits_remaining:C.creditsRemaining}),await n({title:"Success",message:`Generated ${C.flashcards.length} flashcards successfully!`,variant:"success"}),v([]),N(""),o(!1)}catch(C){await n({title:"Generation Error",message:C.message||"Failed to generate flashcards",variant:"error"})}finally{O(!1)}}};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium text-white",children:"Manage Flashcards"}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(_,{onClick:()=>c(!P),variant:"secondary",size:"sm",children:"➕ Add Flashcard"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"AI Mode"}),e.jsx("button",{onClick:()=>o(!F),className:`
                relative inline-flex h-6 w-11 items-center rounded-full transition-colors
                ${F?"bg-primary-500":"bg-gray-600"}
              `,children:e.jsx("span",{className:`
                  inline-block h-4 w-4 transform rounded-full bg-white transition-transform
                  ${F?"translate-x-6":"translate-x-1"}
                `})})]})]})]}),P&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"Add New Flashcard"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Front (Question/Term)"}),e.jsx("textarea",{value:b.front,onChange:r=>L(m=>({...m,front:r.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter the front content..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Back (Answer/Definition)"}),e.jsx("textarea",{value:b.back,onChange:r=>L(m=>({...m,back:r.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter the back content..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Difficulty Level"}),e.jsxs("select",{value:b.difficulty_level,onChange:r=>L(m=>({...m,difficulty_level:parseInt(r.target.value)})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:1,children:"1 - Very Easy"}),e.jsx("option",{value:2,children:"2 - Easy"}),e.jsx("option",{value:3,children:"3 - Medium"}),e.jsx("option",{value:4,children:"4 - Hard"}),e.jsx("option",{value:5,children:"5 - Very Hard"})]})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx(_,{onClick:q,variant:"primary",children:"Add Flashcard"}),e.jsx(_,{onClick:()=>c(!1),variant:"secondary",children:"Cancel"})]})]})]}),z&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"Edit Flashcard"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Front (Question/Term)"}),e.jsx("textarea",{value:D.front,onChange:r=>I(m=>({...m,front:r.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter the front content..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Back (Answer/Definition)"}),e.jsx("textarea",{value:D.back,onChange:r=>I(m=>({...m,back:r.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter the back content..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Difficulty Level"}),e.jsxs("select",{value:D.difficulty_level,onChange:r=>I(m=>({...m,difficulty_level:parseInt(r.target.value)})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:1,children:"1 - Very Easy"}),e.jsx("option",{value:2,children:"2 - Easy"}),e.jsx("option",{value:3,children:"3 - Medium"}),e.jsx("option",{value:4,children:"4 - Hard"}),e.jsx("option",{value:5,children:"5 - Very Hard"})]})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx(_,{onClick:B,variant:"primary",children:"Save Changes"}),e.jsx(_,{onClick:x,variant:"secondary",children:"Cancel"})]})]})]}),F&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"AI Flashcard Generation"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Select Documents"}),e.jsx(W,{selectedDocuments:w,onSelectionChange:v,maxSelection:5})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Number of Flashcards"}),e.jsx("input",{type:"number",min:"1",max:"50",value:T,onChange:r=>A(parseInt(r.target.value)||1),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Credit Cost"}),e.jsxs("div",{className:"px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-primary-400 font-medium",children:[t()," credits"]})]})]}),e.jsx(oe,{value:M,onChange:R}),e.jsx(me,{value:Q,onChange:h}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Custom Instructions (Optional)"}),e.jsx("textarea",{value:G,onChange:r=>N(r.target.value),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Add specific instructions for flashcard generation..."})]}),e.jsx(_,{onClick:a,disabled:w.length===0||k,className:"w-full",variant:"primary",children:k?"Generating...":`Generate ${T} Flashcards`})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("h4",{className:"text-md font-medium text-white",children:["Current Flashcards (",y.length,")"]}),y.length===0?e.jsx("div",{className:"text-center py-8 text-gray-400",children:"No flashcards yet. Add some manually or generate them with AI."}):e.jsx("div",{className:"space-y-2",children:y.map(r=>e.jsx("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600 hover:border-gray-500 transition-colors",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Front"}),e.jsx("p",{className:"text-white font-medium",children:r.front})]}),e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Back"}),e.jsx("p",{className:"text-gray-300",children:r.back})]}),e.jsxs("div",{className:"flex items-center space-x-4 text-xs text-gray-400",children:[r.is_ai_generated&&e.jsx("span",{className:"bg-primary-500/20 text-primary-400 px-2 py-1 rounded",children:"AI Generated"}),r.difficulty_level&&e.jsxs("span",{children:["Difficulty: ",typeof r.difficulty_level=="string"?Y(r.difficulty_level):Y(Z(r.difficulty_level))]}),e.jsxs("span",{children:["Reviewed: ",r.times_reviewed||0," times"]})]})]}),e.jsxs("div",{className:"flex items-center space-x-2 ml-4",children:[e.jsx("button",{onClick:()=>H(r),className:"text-gray-400 hover:text-white p-1",title:"Edit flashcard",children:"✏️"}),e.jsx("button",{onClick:()=>f(r),className:"text-gray-400 hover:text-red-400 p-1",title:"Delete flashcard",children:"🗑️"})]})]})},r.id))})]})]})},ge=({studySetId:s,questions:y,onQuestionAdded:i,onQuestionUpdated:g,onQuestionDeleted:d,onQuestionsGenerated:u})=>{const{alert:n,confirm:E}=J(),{user:S}=V(),[j,F]=p.useState(!1),[o,w]=p.useState([]),[v,T]=p.useState(10),[A,G]=p.useState(""),[N,M]=p.useState(!1),[R,Q]=p.useState(!1),[h,k]=p.useState({question_text:"",question_type:"multiple_choice",options:["","","",""],correct_answers:[],explanation:"",difficulty_level:3}),[O,P]=p.useState(null),[c,b]=p.useState({question_text:"",question_type:"multiple_choice",options:["","","",""],correct_answers:[],explanation:"",difficulty_level:3}),L=async()=>{if(!h.question_text.trim()){await n({title:"Validation Error",message:"Question text is required.",variant:"error"});return}if(h.correct_answers.length===0){await n({title:"Validation Error",message:"At least one correct answer is required.",variant:"error"});return}if((h.question_type==="multiple_choice"||h.question_type==="select_all")&&h.options.filter(a=>a.trim().length>0).length<2){await n({title:"Validation Error",message:"Multiple choice and select all questions require at least 2 options.",variant:"error"});return}try{const t=await fetch(`/api/quiz-questions/study-set/${s}`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`},body:JSON.stringify({question_text:h.question_text.trim(),question_type:h.question_type,options:h.question_type==="multiple_choice"||h.question_type==="select_all"?h.options.filter(r=>r.trim().length>0):null,correct_answers:h.correct_answers,explanation:h.explanation.trim()||null,difficulty_level:h.difficulty_level})});if(!t.ok)throw new Error("Failed to create question");const a=await t.json();i(a.data),k({question_text:"",question_type:"multiple_choice",options:["","","",""],correct_answers:[],explanation:"",difficulty_level:3}),Q(!1),await n({title:"Success",message:"Question added successfully!",variant:"success"})}catch(t){await n({title:"Error",message:t.message||"Failed to add question",variant:"error"})}},z=async t=>{if(await E({title:"Delete Question",message:`Are you sure you want to delete this question?

${t.question_text.substring(0,100)}${t.question_text.length>100?"...":""}`,variant:"danger",confirmText:"Delete",cancelText:"Cancel"}))try{if(!(await fetch(`/api/quiz-questions/${t.id}`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")}`}})).ok)throw new Error("Failed to delete question");d(t.id),await n({title:"Success",message:"Question deleted successfully!",variant:"success"})}catch(r){await n({title:"Error",message:r.message||"Failed to delete question",variant:"error"})}},U=t=>{P(t),b({question_text:t.question_text,question_type:t.question_type,options:t.options||["","","",""],correct_answers:t.correct_answers,explanation:t.explanation||"",difficulty_level:typeof t.difficulty_level=="string"?X(t.difficulty_level):t.difficulty_level||3})},D=async()=>{if(O){if(!c.question_text.trim()){await n({title:"Validation Error",message:"Question text is required.",variant:"error"});return}if(c.correct_answers.length===0){await n({title:"Validation Error",message:"At least one correct answer is required.",variant:"error"});return}if((c.question_type==="multiple_choice"||c.question_type==="select_all")&&c.options.filter(a=>a.trim().length>0).length<2){await n({title:"Validation Error",message:"Multiple choice and select all questions require at least 2 options.",variant:"error"});return}try{const t=await fetch(`/api/quiz-questions/${O.id}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`},body:JSON.stringify({question_text:c.question_text.trim(),question_type:c.question_type,options:c.question_type==="multiple_choice"||c.question_type==="select_all"?c.options.filter(r=>r.trim().length>0):null,correct_answers:c.correct_answers,explanation:c.explanation.trim()||null,difficulty_level:c.difficulty_level})});if(!t.ok)throw new Error("Failed to update question");const a=await t.json();g(a.data),P(null),await n({title:"Success",message:"Question updated successfully!",variant:"success"})}catch(t){await n({title:"Error",message:t.message||"Failed to update question",variant:"error"})}}},I=()=>{P(null),b({question_text:"",question_type:"multiple_choice",options:["","","",""],correct_answers:[],explanation:"",difficulty_level:3})},q=()=>v*3,H=async()=>{if(o.length===0){await n({title:"No Documents Selected",message:"Please select at least one document to generate questions from.",variant:"warning"});return}const t=q();if(S&&S.credits_remaining<t){await n({title:"Insufficient Credits",message:`You need ${t} credits to generate ${v} questions, but you only have ${S.credits_remaining} credits remaining.`,variant:"warning"});return}if(await E({title:"Generate Questions",message:`Generate ${v} questions for ${t} credits?`,confirmText:"Generate",cancelText:"Cancel"})){M(!0);try{const r=await fetch("/api/ai/generate-more-quiz-questions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`},body:JSON.stringify({studySetId:s,documentIds:o,count:v,customPrompt:A.trim()||void 0})});if(!r.ok)throw new Error("Failed to generate questions");const m=await r.json();u(m.data.questions),S&&V.getState().updateUser({credits_remaining:m.data.creditsRemaining}),await n({title:"Success",message:`Generated ${m.data.questions.length} questions successfully!`,variant:"success"}),w([]),G(""),F(!1)}catch(r){await n({title:"Error",message:r.message||"Failed to generate questions",variant:"error"})}finally{M(!1)}}},B=t=>{k(a=>({...a,question_type:t,options:t==="multiple_choice"||t==="select_all"?["","","",""]:[],correct_answers:[]}))},x=(t,a)=>{k(r=>({...r,options:r.options.map((m,C)=>C===t?a:m)}))},f=t=>{k(a=>{const r=a.correct_answers.includes(t);return a.question_type==="multiple_choice"?{...a,correct_answers:r?[]:[t]}:{...a,correct_answers:r?a.correct_answers.filter(m=>m!==t):[...a.correct_answers,t]}})};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Question Management"}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.jsx(_,{onClick:()=>Q(!R),variant:"secondary",size:"sm",children:R?"Cancel":"Add Question"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-sm text-gray-300",children:"AI Mode"}),e.jsx("button",{onClick:()=>F(!j),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${j?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${j?"translate-x-6":"translate-x-1"}`})})]})]})]}),R&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"Add New Question"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Question Text"}),e.jsx("textarea",{value:h.question_text,onChange:t=>k(a=>({...a,question_text:t.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter your question..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Question Type"}),e.jsxs("select",{value:h.question_type,onChange:t=>B(t.target.value),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:"multiple_choice",children:"Multiple Choice"}),e.jsx("option",{value:"select_all",children:"Select All That Apply"}),e.jsx("option",{value:"true_false",children:"True/False"}),e.jsx("option",{value:"short_answer",children:"Short Answer"})]})]}),(h.question_type==="multiple_choice"||h.question_type==="select_all")&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Answer Options"}),e.jsx("div",{className:"space-y-2",children:h.options.map((t,a)=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{type:"button",onClick:()=>f(t),className:`flex-shrink-0 w-5 h-5 border-2 ${h.question_type==="multiple_choice"?"rounded-full":"rounded"} ${h.correct_answers.includes(t)?"bg-primary-500 border-primary-500":"border-gray-400"} flex items-center justify-center`,disabled:!t.trim(),children:h.correct_answers.includes(t)&&e.jsx("span",{className:"text-white text-xs",children:"✓"})}),e.jsx("input",{type:"text",value:t,onChange:r=>x(a,r.target.value),className:"flex-1 px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",placeholder:`Option ${a+1}`})]},a))}),e.jsx("p",{className:"text-xs text-gray-400 mt-1",children:h.question_type==="multiple_choice"?"Click the circle to mark the correct answer":"Click the squares to mark all correct answers"})]}),h.question_type==="true_false"&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Correct Answer"}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx("button",{type:"button",onClick:()=>k(t=>({...t,correct_answers:["True"]})),className:`px-4 py-2 rounded-lg border ${h.correct_answers.includes("True")?"bg-primary-500 border-primary-500 text-white":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:"True"}),e.jsx("button",{type:"button",onClick:()=>k(t=>({...t,correct_answers:["False"]})),className:`px-4 py-2 rounded-lg border ${h.correct_answers.includes("False")?"bg-primary-500 border-primary-500 text-white":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:"False"})]})]}),h.question_type==="short_answer"&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Acceptable Answers (one per line)"}),e.jsx("textarea",{value:h.correct_answers.join(`
`),onChange:t=>k(a=>({...a,correct_answers:t.target.value.split(`
`).filter(r=>r.trim())})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter acceptable answers, one per line..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Explanation (Optional)"}),e.jsx("textarea",{value:h.explanation,onChange:t=>k(a=>({...a,explanation:t.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:2,placeholder:"Explain the correct answer..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Difficulty Level"}),e.jsxs("select",{value:h.difficulty_level,onChange:t=>k(a=>({...a,difficulty_level:parseInt(t.target.value)})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:1,children:"1 - Very Easy"}),e.jsx("option",{value:2,children:"2 - Easy"}),e.jsx("option",{value:3,children:"3 - Medium"}),e.jsx("option",{value:4,children:"4 - Hard"}),e.jsx("option",{value:5,children:"5 - Very Hard"})]})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx(_,{onClick:L,variant:"primary",children:"Add Question"}),e.jsx(_,{onClick:()=>Q(!1),variant:"secondary",children:"Cancel"})]})]})]}),O&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"Edit Question"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Question Text"}),e.jsx("textarea",{value:c.question_text,onChange:t=>b(a=>({...a,question_text:t.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter your question..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Question Type"}),e.jsxs("select",{value:c.question_type,onChange:t=>b(a=>({...a,question_type:t.target.value,options:t.target.value==="multiple_choice"||t.target.value==="select_all"?["","","",""]:[],correct_answers:[]})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:"multiple_choice",children:"Multiple Choice"}),e.jsx("option",{value:"select_all",children:"Select All That Apply"}),e.jsx("option",{value:"true_false",children:"True/False"}),e.jsx("option",{value:"short_answer",children:"Short Answer"})]})]}),(c.question_type==="multiple_choice"||c.question_type==="select_all")&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Answer Options"}),e.jsx("div",{className:"space-y-2",children:c.options.map((t,a)=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{type:"button",onClick:()=>{const r=c.correct_answers.includes(t);c.question_type==="multiple_choice"?b(m=>({...m,correct_answers:r?[]:[t]})):b(m=>({...m,correct_answers:r?m.correct_answers.filter(C=>C!==t):[...m.correct_answers,t]}))},className:`flex-shrink-0 w-5 h-5 border-2 ${c.question_type==="multiple_choice"?"rounded-full":"rounded"} ${c.correct_answers.includes(t)?"bg-primary-500 border-primary-500":"border-gray-400"} flex items-center justify-center`,disabled:!t.trim(),children:c.correct_answers.includes(t)&&e.jsx("span",{className:"text-white text-xs",children:"✓"})}),e.jsx("input",{type:"text",value:t,onChange:r=>b(m=>({...m,options:m.options.map((C,ee)=>ee===a?r.target.value:C)})),className:"flex-1 px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",placeholder:`Option ${a+1}`})]},a))}),e.jsx("p",{className:"text-xs text-gray-400 mt-1",children:c.question_type==="multiple_choice"?"Click the circle to mark the correct answer":"Click the squares to mark all correct answers"})]}),c.question_type==="true_false"&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Correct Answer"}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx("button",{type:"button",onClick:()=>b(t=>({...t,correct_answers:["True"]})),className:`px-4 py-2 rounded-lg border ${c.correct_answers.includes("True")?"bg-primary-500 border-primary-500 text-white":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:"True"}),e.jsx("button",{type:"button",onClick:()=>b(t=>({...t,correct_answers:["False"]})),className:`px-4 py-2 rounded-lg border ${c.correct_answers.includes("False")?"bg-primary-500 border-primary-500 text-white":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:"False"})]})]}),c.question_type==="short_answer"&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Acceptable Answers (one per line)"}),e.jsx("textarea",{value:c.correct_answers.join(`
`),onChange:t=>b(a=>({...a,correct_answers:t.target.value.split(`
`).filter(r=>r.trim())})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter acceptable answers, one per line..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Explanation (Optional)"}),e.jsx("textarea",{value:c.explanation,onChange:t=>b(a=>({...a,explanation:t.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:2,placeholder:"Explain the correct answer..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Difficulty Level"}),e.jsxs("select",{value:c.difficulty_level,onChange:t=>b(a=>({...a,difficulty_level:parseInt(t.target.value)})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:1,children:"1 - Very Easy"}),e.jsx("option",{value:2,children:"2 - Easy"}),e.jsx("option",{value:3,children:"3 - Medium"}),e.jsx("option",{value:4,children:"4 - Hard"}),e.jsx("option",{value:5,children:"5 - Very Hard"})]})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx(_,{onClick:D,variant:"primary",children:"Save Changes"}),e.jsx(_,{onClick:I,variant:"secondary",children:"Cancel"})]})]})]}),j&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"AI Question Generation"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Select Documents"}),e.jsx(W,{selectedDocuments:o,onSelectionChange:w,maxSelection:5})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Number of Questions"}),e.jsx("input",{type:"number",min:"1",max:"50",value:v,onChange:t=>T(parseInt(t.target.value)||1),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Credit Cost"}),e.jsxs("div",{className:"px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-primary-400 font-medium",children:[q()," credits"]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Custom Instructions (Optional)"}),e.jsx("textarea",{value:A,onChange:t=>G(t.target.value),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Add specific instructions for question generation..."})]}),e.jsx(_,{onClick:H,disabled:o.length===0||N,className:"w-full",variant:"primary",children:N?"Generating...":`Generate ${v} Questions`})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("h4",{className:"text-md font-medium text-white",children:["Current Questions (",y.length,")"]}),y.length===0?e.jsx("div",{className:"text-center py-8 text-gray-400",children:"No questions yet. Add some manually or generate them with AI."}):e.jsx("div",{className:"space-y-2",children:y.map(t=>e.jsx("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600 hover:border-gray-500 transition-colors",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Question"}),e.jsx("p",{className:"text-white font-medium",children:t.question_text})]}),e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Type"}),e.jsx("p",{className:"text-gray-300 capitalize",children:t.question_type.replace("_"," ")})]}),t.options&&e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Options"}),e.jsx("ul",{className:"text-gray-300 text-sm",children:t.options.map((a,r)=>e.jsxs("li",{className:`${t.correct_answers.includes(a)?"text-green-400 font-medium":""}`,children:[r+1,". ",a]},`${t.id}-option-${r}`))})]}),t.explanation&&e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Explanation"}),e.jsx("p",{className:"text-gray-300 text-sm",children:t.explanation})]}),e.jsxs("div",{className:"flex items-center space-x-4 text-xs text-gray-400",children:[t.is_ai_generated&&e.jsx("span",{className:"bg-primary-500/20 text-primary-400 px-2 py-1 rounded",children:"AI Generated"}),t.difficulty_level&&e.jsxs("span",{children:["Difficulty: ",typeof t.difficulty_level=="string"?Y(t.difficulty_level):Y(Z(t.difficulty_level))]}),e.jsxs("span",{children:["Attempted: ",t.times_attempted||0," times"]}),e.jsxs("span",{children:["Correct: ",t.times_correct||0," times"]})]})]}),e.jsxs("div",{className:"flex items-center space-x-2 ml-4",children:[e.jsx("button",{onClick:()=>U(t),className:"text-gray-400 hover:text-primary-400 p-1",title:"Edit question",children:"✏️"}),e.jsx("button",{onClick:()=>z(t),className:"text-gray-400 hover:text-red-400 p-1",title:"Delete question",children:"🗑️"})]})]})},t.id))})]})]})},be=()=>{const{id:s}=re(),y=se(),{studySetContent:i,isLoading:g,error:d,fetchStudySetContent:u}=K(),{alert:n,confirm:E,prompt:S}=J(),[j,F]=p.useState(null),[o,w]=p.useState("study"),[v,T]=p.useState("flashcards"),[A,G]=p.useState([]),[N,M]=p.useState([]),[R,Q]=p.useState("");p.useEffect(()=>{s&&u(s).catch(console.error)},[s,u]),p.useEffect(()=>{i!=null&&i.studySet&&(Q(i.studySet.name),G(i.flashcards||[]),M(i.questions||[]))},[i]);const h=async()=>{if(!(!s||!j))try{await K.getState().startStudySession(s,j),y(`/study/${s}/${j}`)}catch(x){await n({title:"Error",message:x.message||"Failed to start study session",variant:"error"})}},k=async()=>{if(!s||!(i!=null&&i.studySet))return;const x=await S({title:"Rename Study Set",message:"Enter a new name for this study set:",defaultValue:i.studySet.name});if(!(x===null||x.trim()===i.studySet.name)){if(!x.trim()){await n({title:"Invalid Name",message:"Study set name cannot be empty.",variant:"error"});return}try{if(!(await fetch(`/api/study-sets/${s}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`},body:JSON.stringify({name:x.trim()})})).ok)throw new Error("Failed to rename study set");Q(x.trim()),await n({title:"Success",message:"Study set renamed successfully!",variant:"success"}),await u(s)}catch(f){await n({title:"Error",message:f.message||"Failed to rename study set",variant:"error"})}}},O=async()=>{if(!(!s||!(i!=null&&i.studySet)||!await E({title:"Delete Study Set",message:`Are you sure you want to delete "${i.studySet.name}"?

This action cannot be undone and will delete all flashcards and quiz questions in this set.`,variant:"danger",confirmText:"Delete Study Set",cancelText:"Cancel"})))try{if(!(await fetch(`/api/study-sets/${s}`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")}`}})).ok)throw new Error("Failed to delete study set");await n({title:"Success",message:"Study set deleted successfully!",variant:"success"}),y("/dashboard")}catch(f){await n({title:"Error",message:f.message||"Failed to delete study set",variant:"error"})}},P=x=>{G(f=>[...f,x])},c=x=>{G(f=>f.map(t=>t.id===x.id?x:t))},b=x=>{G(f=>f.filter(t=>t.id!==x))},L=x=>{G(f=>[...f,...x])},z=x=>{M(f=>[...f,x])},U=x=>{M(f=>f.map(t=>t.id===x.id?x:t))},D=x=>{M(f=>f.filter(t=>t.id!==x))},I=x=>{M(f=>[...f,...x])};if(g)return e.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e.jsxs("div",{className:"flex items-center justify-center py-12",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"}),e.jsx("span",{className:"ml-3 text-gray-400",children:"Loading study set..."})]})});if(d||!(i!=null&&i.studySet))return e.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"text-red-400 mb-4",children:d||"Study set not found"}),e.jsx(_,{onClick:()=>y("/dashboard"),variant:"secondary",children:"Back to Dashboard"})]})});const{studySet:q}=i,H=A&&A.length>0,B=N&&N.length>0;return e.jsxs("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("button",{onClick:()=>y("/dashboard"),className:"text-gray-400 hover:text-white mb-4 flex items-center",children:"← Back to Dashboard"}),e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("h1",{className:"text-3xl font-bold text-white",children:R}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(_,{onClick:k,variant:"secondary",size:"sm",children:"✏️ Rename"}),e.jsx(_,{onClick:O,variant:"danger",size:"sm",children:"🗑️ Delete"})]})]}),e.jsxs("div",{className:"flex items-center space-x-4 text-sm text-gray-400",children:[e.jsx("span",{className:"capitalize",children:q.type}),q.is_ai_generated&&e.jsx("span",{className:"bg-primary-500/20 text-primary-400 px-2 py-1 rounded",children:"AI Generated"}),e.jsxs("span",{children:["Created ",new Date(q.created_at).toLocaleDateString()]})]})]}),e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"border-b border-gray-600",children:e.jsxs("nav",{className:"-mb-px flex space-x-8",children:[e.jsx("button",{onClick:()=>w("study"),className:`
                py-2 px-1 border-b-2 font-medium text-sm transition-colors
                ${o==="study"?"border-primary-500 text-primary-400":"border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300"}
              `,children:"📚 Study Mode"}),e.jsx("button",{onClick:()=>w("manage"),className:`
                py-2 px-1 border-b-2 font-medium text-sm transition-colors
                ${o==="manage"?"border-primary-500 text-primary-400":"border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300"}
              `,children:"⚙️ Manage Content"})]})})}),o==="study"&&e.jsx(e.Fragment,{children:e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 mb-6",children:[e.jsx("h2",{className:"text-xl font-semibold text-white mb-4",children:"Choose Study Mode"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[H&&e.jsx("div",{className:`
                    p-4 rounded-lg border-2 cursor-pointer transition-all
                    ${j==="flashcards"?"border-primary-500 bg-primary-500/10":"border-gray-600 hover:border-gray-500"}
                  `,onClick:()=>F("flashcards"),children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"text-2xl",children:"🃏"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-white",children:"Flashcard Review"}),e.jsxs("p",{className:"text-sm text-gray-400",children:[A==null?void 0:A.length," flashcards • Interactive review"]})]})]})}),B&&e.jsx("div",{className:`
                    p-4 rounded-lg border-2 cursor-pointer transition-all
                    ${j==="quiz"?"border-primary-500 bg-primary-500/10":"border-gray-600 hover:border-gray-500"}
                  `,onClick:()=>F("quiz"),children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"text-2xl",children:"📝"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-white",children:"Quiz Practice"}),e.jsxs("p",{className:"text-sm text-gray-400",children:[N==null?void 0:N.length," questions • Test your knowledge"]})]})]})})]}),e.jsx(_,{onClick:h,disabled:!j,className:"w-full",size:"lg",children:j?`Start ${j==="flashcards"?"Flashcard Review":"Quiz Practice"}`:"Select a study mode"})]})}),o==="manage"&&s&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 mb-6",children:[e.jsxs("div",{className:"flex space-x-1 mb-6",children:[e.jsxs("button",{onClick:()=>T("flashcards"),className:`
                py-2 px-4 rounded-lg font-medium text-sm transition-colors
                ${v==="flashcards"?"bg-primary-500 text-white":"bg-background-primary text-gray-400 hover:text-gray-300"}
              `,children:["📚 Flashcards (",A.length,")"]}),e.jsxs("button",{onClick:()=>T("quiz"),className:`
                py-2 px-4 rounded-lg font-medium text-sm transition-colors
                ${v==="quiz"?"bg-primary-500 text-white":"bg-background-primary text-gray-400 hover:text-gray-300"}
              `,children:["❓ Quiz Questions (",N.length,")"]})]}),v==="flashcards"&&e.jsx(xe,{studySetId:s,flashcards:A,onFlashcardAdded:P,onFlashcardUpdated:c,onFlashcardDeleted:b,onFlashcardsGenerated:L}),v==="quiz"&&e.jsx(ge,{studySetId:s,questions:N,onQuestionAdded:z,onQuestionUpdated:U,onQuestionDeleted:D,onQuestionsGenerated:I})]}),e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6",children:[e.jsx("h3",{className:"text-lg font-medium text-white mb-4",children:"Study Set Details"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Content"}),e.jsxs("div",{className:"space-y-1 text-sm text-gray-400",children:[H&&e.jsxs("div",{children:[A.length," flashcards"]}),B&&e.jsxs("div",{children:[N==null?void 0:N.length," quiz questions"]}),!H&&!B&&e.jsx("div",{className:"text-gray-500",children:"No content yet"})]})]}),q.source_documents&&q.source_documents.length>0&&e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Source Documents"}),e.jsx("div",{className:"space-y-1 text-sm text-gray-400",children:q.source_documents.map((x,f)=>e.jsx("div",{children:x.filename},f))})]}),q.custom_prompt&&e.jsxs("div",{className:"md:col-span-2",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Custom Instructions"}),e.jsx("p",{className:"text-sm text-gray-400",children:q.custom_prompt})]})]})]})]})};export{be as StudySetPage};
