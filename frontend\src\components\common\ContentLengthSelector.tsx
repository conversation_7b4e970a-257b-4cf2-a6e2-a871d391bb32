import React from 'react';
import { ContentLength } from '../../../../shared/types';

interface ContentLengthSelectorProps {
  value: ContentLength;
  onChange: (length: ContentLength) => void;
  className?: string;
  disabled?: boolean;
  label?: string;
}

const contentLengthOptions = [
  { 
    value: ContentLength.SHORT, 
    label: 'Short', 
    description: 'Concise answers (1-2 sentences)',
    icon: '📝'
  },
  { 
    value: ContentLength.MEDIUM, 
    label: 'Medium', 
    description: 'Balanced detail (2-3 sentences)',
    icon: '📄'
  },
  { 
    value: ContentLength.LONG, 
    label: 'Long', 
    description: 'Comprehensive answers (3-5 sentences)',
    icon: '📋'
  }
];

const getContentLengthColor = (length: ContentLength): string => {
  switch (length) {
    case ContentLength.SHORT:
      return 'bg-emerald-100 text-emerald-800 border-emerald-200 hover:bg-emerald-200';
    case ContentLength.MEDIUM:
      return 'bg-amber-100 text-amber-800 border-amber-200 hover:bg-amber-200';
    case ContentLength.LONG:
      return 'bg-indigo-100 text-indigo-800 border-indigo-200 hover:bg-indigo-200';
    default:
      return 'bg-amber-100 text-amber-800 border-amber-200 hover:bg-amber-200';
  }
};

const getSelectedContentLengthColor = (length: ContentLength): string => {
  switch (length) {
    case ContentLength.SHORT:
      return 'bg-emerald-500 text-white border-emerald-500';
    case ContentLength.MEDIUM:
      return 'bg-amber-500 text-white border-amber-500';
    case ContentLength.LONG:
      return 'bg-indigo-500 text-white border-indigo-500';
    default:
      return 'bg-amber-500 text-white border-amber-500';
  }
};

export const ContentLengthSelector: React.FC<ContentLengthSelectorProps> = ({
  value,
  onChange,
  className = '',
  disabled = false,
  label = 'Content Length'
}) => {
  return (
    <div className={`space-y-3 ${className}`}>
      <label className="block text-sm font-medium text-gray-700">
        {label}
      </label>
      <div className="grid grid-cols-3 gap-3">
        {contentLengthOptions.map((option) => {
          const isSelected = value === option.value;
          const colorClasses = isSelected 
            ? getSelectedContentLengthColor(option.value)
            : getContentLengthColor(option.value);
          
          return (
            <button
              key={option.value}
              type="button"
              onClick={() => !disabled && onChange(option.value)}
              disabled={disabled}
              className={`
                relative p-4 rounded-lg border-2 text-sm font-medium transition-all duration-200
                ${colorClasses}
                ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                ${isSelected ? 'ring-2 ring-offset-2 ring-blue-500' : ''}
                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
              `}
              title={option.description}
            >
              <div className="text-center space-y-2">
                <div className="text-2xl">{option.icon}</div>
                <div className="font-semibold">{option.label}</div>
                <div className={`text-xs ${isSelected ? 'text-white/90' : 'opacity-75'}`}>
                  {option.description}
                </div>
              </div>
              {isSelected && (
                <div className="absolute top-2 right-2">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              )}
            </button>
          );
        })}
      </div>
      <p className="text-xs text-gray-500">
        Choose how detailed you want the flashcard answers to be. This affects the length and depth of explanations.
      </p>
    </div>
  );
};

export default ContentLengthSelector;
