import React from 'react';
import { DifficultyLevel } from '../../../../shared/types';

interface DifficultySelectorProps {
  value: DifficultyLevel;
  onChange: (difficulty: DifficultyLevel) => void;
  className?: string;
  disabled?: boolean;
  label?: string;
}

const difficultyOptions = [
  { value: DifficultyLevel.EASY, label: 'Easy', description: 'Basic facts and definitions' },
  { value: DifficultyLevel.MEDIUM, label: 'Medium', description: 'Moderate understanding required' },
  { value: DifficultyLevel.HARD, label: 'Hard', description: 'Deep analysis and critical thinking' },
  { value: DifficultyLevel.COLLEGE, label: 'College', description: 'Undergraduate level complexity' },
  { value: DifficultyLevel.GRADUATE, label: 'Graduate', description: 'Advanced graduate study' },
  { value: DifficultyLevel.PHD, label: 'PhD', description: 'Research-level expertise' }
];

const getDifficultyColor = (difficulty: DifficultyLevel): string => {
  switch (difficulty) {
    case DifficultyLevel.EASY:
      return 'bg-green-100 text-green-800 border-green-200 hover:bg-green-200';
    case DifficultyLevel.MEDIUM:
      return 'bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200';
    case DifficultyLevel.HARD:
      return 'bg-orange-100 text-orange-800 border-orange-200 hover:bg-orange-200';
    case DifficultyLevel.COLLEGE:
      return 'bg-purple-100 text-purple-800 border-purple-200 hover:bg-purple-200';
    case DifficultyLevel.GRADUATE:
      return 'bg-red-100 text-red-800 border-red-200 hover:bg-red-200';
    case DifficultyLevel.PHD:
      return 'bg-gray-100 text-gray-800 border-gray-200 hover:bg-gray-200';
    default:
      return 'bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200';
  }
};

const getSelectedDifficultyColor = (difficulty: DifficultyLevel): string => {
  switch (difficulty) {
    case DifficultyLevel.EASY:
      return 'bg-green-500 text-white border-green-500';
    case DifficultyLevel.MEDIUM:
      return 'bg-blue-500 text-white border-blue-500';
    case DifficultyLevel.HARD:
      return 'bg-orange-500 text-white border-orange-500';
    case DifficultyLevel.COLLEGE:
      return 'bg-purple-500 text-white border-purple-500';
    case DifficultyLevel.GRADUATE:
      return 'bg-red-500 text-white border-red-500';
    case DifficultyLevel.PHD:
      return 'bg-gray-700 text-white border-gray-700';
    default:
      return 'bg-blue-500 text-white border-blue-500';
  }
};

export const DifficultySelector: React.FC<DifficultySelectorProps> = ({
  value,
  onChange,
  className = '',
  disabled = false,
  label = 'Difficulty Level'
}) => {
  return (
    <div className={`space-y-3 ${className}`}>
      <label className="block text-sm font-medium text-gray-700">
        {label}
      </label>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
        {difficultyOptions.map((option) => {
          const isSelected = value === option.value;
          const colorClasses = isSelected 
            ? getSelectedDifficultyColor(option.value)
            : getDifficultyColor(option.value);
          
          return (
            <button
              key={option.value}
              type="button"
              onClick={() => !disabled && onChange(option.value)}
              disabled={disabled}
              className={`
                relative p-3 rounded-lg border-2 text-sm font-medium transition-all duration-200
                ${colorClasses}
                ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                ${isSelected ? 'ring-2 ring-offset-2 ring-blue-500' : ''}
                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
              `}
              title={option.description}
            >
              <div className="text-center">
                <div className="font-semibold">{option.label}</div>
                <div className={`text-xs mt-1 ${isSelected ? 'text-white/90' : 'opacity-75'}`}>
                  {option.description}
                </div>
              </div>
              {isSelected && (
                <div className="absolute top-1 right-1">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              )}
            </button>
          );
        })}
      </div>
      <p className="text-xs text-gray-500">
        Select the appropriate difficulty level for your flashcards. This affects the complexity of questions and answers generated.
      </p>
    </div>
  );
};

export default DifficultySelector;
