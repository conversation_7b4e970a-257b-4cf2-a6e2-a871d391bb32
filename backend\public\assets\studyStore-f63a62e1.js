import{c as l}from"./index-fc7af858.js";const I=l((o,a)=>({currentSession:null,studySetContent:null,studySets:[],sessions:[],isLoading:!1,error:null,actionHistory:[],currentActionIndex:-1,canUndo:!1,canRedo:!1,fetchStudySetContent:async(t,e=!1)=>{var r;const{studySetContent:n}=a();if(!(e&&((r=n==null?void 0:n.studySet)==null?void 0:r.id)===t)){o({isLoading:!0,error:null});try{const s=localStorage.getItem("auth_token"),i=await fetch(`/api/study-sets/${t}/content`,{headers:{Authorization:`Bearer ${s}`}});if(!i.ok){const d=await i.json();throw new Error(d.error||"Failed to fetch study set content")}const c=await i.json();if(c.success)o({studySetContent:{studySet:c.data.studySet,flashcards:c.data.studySet.type==="flashcards"?c.data.content:void 0,questions:c.data.studySet.type==="quiz"?c.data.content:void 0},isLoading:!1});else throw new Error(c.error)}catch(s){throw o({error:s.message||"Failed to fetch study set content",isLoading:!1}),s}}},startStudySession:async(t,e)=>{var c,d,u;const{studySetContent:n,fetchStudySetContent:r}=a();(!n||((c=n.studySet)==null?void 0:c.id)!==t)&&await r(t);const s=a().studySetContent;if(!s)throw new Error("Failed to load study set content");const i=e==="flashcards"?((d=s.flashcards)==null?void 0:d.length)||0:((u=s.questions)==null?void 0:u.length)||0;if(i===0)throw new Error("No study materials found in this set");o({currentSession:{studySetId:t,type:e,startTime:new Date,currentIndex:0,totalItems:i,reviewedItems:[],flaggedItems:[],correctAnswers:e==="quiz"?0:void 0,timeSpent:0}})},endStudySession:()=>{o({currentSession:null})},nextItem:()=>{const{currentSession:t,addToHistory:e}=a();if(!t)return;const n=t.currentIndex===t.totalItems-1?0:t.currentIndex+1;e({type:"NEXT_ITEM",payload:{fromIndex:t.currentIndex,toIndex:n},previousState:{currentIndex:t.currentIndex},timestamp:Date.now()}),o({currentSession:{...t,currentIndex:n}})},previousItem:()=>{const{currentSession:t,addToHistory:e}=a();if(!t)return;const n=t.currentIndex===0?t.totalItems-1:t.currentIndex-1;e({type:"PREVIOUS_ITEM",payload:{fromIndex:t.currentIndex,toIndex:n},previousState:{currentIndex:t.currentIndex},timestamp:Date.now()}),o({currentSession:{...t,currentIndex:n}})},goToItem:t=>{const{currentSession:e}=a();if(!e)return;const n=Math.max(0,Math.min(t,e.totalItems-1));o({currentSession:{...e,currentIndex:n}})},toggleFlag:t=>{const{currentSession:e,addToHistory:n}=a();if(!e)return;const r=e.flaggedItems.includes(t),s=r?e.flaggedItems.filter(i=>i!==t):[...e.flaggedItems,t];n({type:"TOGGLE_FLAG",payload:{itemId:t,wasFlagged:r},previousState:{flaggedItems:e.flaggedItems},timestamp:Date.now()}),o({currentSession:{...e,flaggedItems:s}})},markReviewed:t=>{const{currentSession:e}=a();e&&(e.reviewedItems.includes(e.currentIndex)||o({currentSession:{...e,reviewedItems:[...e.reviewedItems,e.currentIndex]}}))},submitQuizAnswer:(t,e,n)=>{const{currentSession:r,markReviewed:s}=a();!r||r.type!=="quiz"||(s(t),n&&o({currentSession:{...r,correctAnswers:(r.correctAnswers||0)+1}}))},updateTimeSpent:t=>{const{currentSession:e}=a();e&&o({currentSession:{...e,timeSpent:e.timeSpent+t}})},addToHistory:t=>{const{actionHistory:e,currentActionIndex:n}=a(),r=e.slice(0,n+1);r.push(t);const s=r.slice(-50);o({actionHistory:s,currentActionIndex:s.length-1,canUndo:s.length>0,canRedo:!1})},undo:()=>{const{actionHistory:t,currentActionIndex:e,currentSession:n}=a();if(e<0||!n)return;const r=t[e];o({currentSession:{...n,...r.previousState},currentActionIndex:e-1,canUndo:e>0,canRedo:!0})},redo:()=>{const{actionHistory:t,currentActionIndex:e,currentSession:n}=a();if(e>=t.length-1||!n)return;const r=e+1,s=t[r];switch(s.type){case"NEXT_ITEM":a().nextItem();break;case"PREVIOUS_ITEM":a().previousItem();break;case"TOGGLE_FLAG":a().toggleFlag(s.payload.itemId);break;case"MARK_REVIEWED":a().markReviewed(s.payload.itemId);break}o({currentActionIndex:r,canUndo:!0,canRedo:r<t.length-1})},clearHistory:()=>{o({actionHistory:[],currentActionIndex:-1,canUndo:!1,canRedo:!1})},fetchStudySets:async()=>{o({isLoading:!0,error:null});try{const t=localStorage.getItem("auth_token"),e=await fetch("/api/study-sets",{headers:{Authorization:`Bearer ${t}`}});if(!e.ok){const r=await e.json();throw new Error(r.error||"Failed to fetch study sets")}const n=await e.json();if(n.success)o({studySets:n.data,isLoading:!1});else throw new Error(n.error)}catch(t){throw o({error:t.message||"Failed to fetch study sets",isLoading:!1}),t}},fetchStudySessions:async(t="30d")=>{o({isLoading:!0,error:null});try{const e=localStorage.getItem("auth_token"),n=await fetch(`/api/study-sessions?timeRange=${t}`,{headers:{Authorization:`Bearer ${e}`}});if(!n.ok){const s=await n.json();throw new Error(s.error||"Failed to fetch study sessions")}const r=await n.json();if(r.success){const s=r.data.map(i=>({...i,startTime:new Date(i.startTime),endTime:i.endTime?new Date(i.endTime):void 0}));o({sessions:s,isLoading:!1})}else throw new Error(r.error)}catch(e){throw o({error:e.message||"Failed to fetch study sessions",isLoading:!1}),e}}}));export{I as u};
