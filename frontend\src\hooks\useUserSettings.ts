import { useState, useEffect, useCallback } from 'react';

export interface UserSettings {
  id: string;
  user_id: string;
  skip_delete_confirmations: boolean;
  shuffle_flashcards: boolean;
  created_at: string;
  updated_at: string;
}

export interface UserSettingsUpdate {
  skip_delete_confirmations?: boolean;
  shuffle_flashcards?: boolean;
}

interface UseUserSettingsReturn {
  settings: UserSettings | null;
  loading: boolean;
  error: string | null;
  updateSettings: (updates: UserSettingsUpdate) => Promise<void>;
  refetch: () => Promise<void>;
}

export const useUserSettings = (): UseUserSettingsReturn => {
  const [settings, setSettings] = useState<UserSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSettings = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch('/api/user/settings', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch user settings');
      }

      const data = await response.json();
      setSettings(data.data);
    } catch (err: any) {
      setError(err.message);
      console.error('Error fetching user settings:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const updateSettings = useCallback(async (updates: UserSettingsUpdate) => {
    try {
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch('/api/user/settings', {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updates)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update user settings');
      }

      const data = await response.json();
      setSettings(data.data);
    } catch (err: any) {
      setError(err.message);
      console.error('Error updating user settings:', err);
      throw err; // Re-throw to allow caller to handle
    }
  }, []);

  const refetch = useCallback(async () => {
    await fetchSettings();
  }, [fetchSettings]);

  useEffect(() => {
    fetchSettings();
  }, [fetchSettings]);

  return {
    settings,
    loading,
    error,
    updateSettings,
    refetch
  };
};
