import{u as N,a as v,r as t,j as e,I as w,B as u}from"./index-fc7af858.js";const k=()=>{const n=N(),{alert:m}=v(),[l,h]=t.useState(""),[s,o]=t.useState("flashcards"),[x,y]=t.useState({}),[p,c]=t.useState(!1),b=async r=>{const a=localStorage.getItem("auth_token"),i=await fetch("/api/study-sets",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${a}`},body:JSON.stringify(r)});if(!i.ok){const j=await i.json();throw new Error(j.error||"Failed to create study set")}const d=await i.json();if(d.success)return d.data;throw new Error(d.error)},g=()=>{const r={};return l.trim()||(r.name="Study set name is required"),y(r),Object.keys(r).length===0},f=async r=>{if(r.preventDefault(),!!g()){c(!0);try{const a=await b({name:l.trim(),type:s});n(`/study-sets/${a.id}`)}catch(a){await m({title:"Error",message:a.message||"Failed to create study set",variant:"error"})}finally{c(!1)}}};return e.jsx("div",{className:"min-h-screen bg-background-primary text-white",children:e.jsxs("div",{className:"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Create New Study Set"}),e.jsx("p",{className:"text-gray-400",children:"Create an empty study set that you can populate with flashcards or quiz questions"})]}),e.jsx("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:e.jsxs("form",{onSubmit:f,className:"space-y-6",children:[e.jsx(w,{label:"Study Set Name",value:l,onChange:h,placeholder:"e.g., Biology Chapter 5, History Final Review",error:x.name,required:!0,className:"w-full"}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Primary Content Type"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsx("div",{className:`
                    p-4 rounded-lg border-2 cursor-pointer transition-all
                    ${s==="flashcards"?"border-primary-500 bg-primary-500/10":"border-gray-600 hover:border-gray-500"}
                  `,onClick:()=>o("flashcards"),children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:`
                      w-4 h-4 rounded-full border-2 flex-shrink-0
                      ${s==="flashcards"?"border-primary-500 bg-primary-500":"border-gray-500"}
                    `,children:s==="flashcards"&&e.jsx("div",{className:"w-full h-full rounded-full bg-white scale-50"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-white",children:"Flashcards"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Question and answer cards for memorization"})]})]})}),e.jsx("div",{className:`
                    p-4 rounded-lg border-2 cursor-pointer transition-all
                    ${s==="quiz"?"border-primary-500 bg-primary-500/10":"border-gray-600 hover:border-gray-500"}
                  `,onClick:()=>o("quiz"),children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:`
                      w-4 h-4 rounded-full border-2 flex-shrink-0
                      ${s==="quiz"?"border-primary-500 bg-primary-500":"border-gray-500"}
                    `,children:s==="quiz"&&e.jsx("div",{className:"w-full h-full rounded-full bg-white scale-50"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-white",children:"Quiz Questions"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Multiple choice, true/false, and short answer questions"})]})]})})]})]}),e.jsx("div",{className:"bg-blue-500/10 border border-blue-500/30 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:"w-5 h-5 text-blue-400 flex-shrink-0 mt-0.5",children:"💡"}),e.jsxs("div",{className:"text-sm",children:[e.jsx("p",{className:"text-blue-400 font-medium mb-1",children:"What happens next?"}),e.jsx("p",{className:"text-blue-300",children:"After creating your study set, you'll be able to add content manually or use AI to generate flashcards and quiz questions from your documents."})]})]})}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(u,{type:"button",variant:"secondary",onClick:()=>n("/dashboard"),className:"flex-1",children:"Cancel"}),e.jsx(u,{type:"submit",variant:"primary",isLoading:p,className:"flex-1",children:"Create Study Set"})]})]})})]})})};export{k as CreateStudySetPage};
