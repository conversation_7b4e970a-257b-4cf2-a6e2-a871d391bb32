import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  HiRefresh,
  HiExclamationCircle,
  HiInformationCircle
} from 'react-icons/hi';
import { Button } from '../common/Button';

interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  interval: 'month' | 'year';
  features: string[];
  popular?: boolean;
  current?: boolean;
}

interface SubscriptionData {
  currentPlan: SubscriptionPlan | null;
  status: 'active' | 'canceled' | 'past_due' | 'trialing';
  nextBillingDate?: string;
  cancelAtPeriodEnd?: boolean;
}

const subscriptionPlans: SubscriptionPlan[] = [
  {
    id: 'free',
    name: 'Free',
    price: 0,
    interval: 'month',
    features: [
      '10 AI generations per month',
      'Basic flashcards and quizzes',
      'Limited document uploads',
      'Basic analytics'
    ]
  },
  {
    id: 'pro_monthly',
    name: 'Pro',
    price: 9.99,
    interval: 'month',
    popular: true,
    features: [
      'Unlimited AI generations',
      'Advanced study modes',
      'Unlimited document uploads',
      'Advanced analytics and insights',
      'Priority support',
      'Export capabilities'
    ]
  },
  {
    id: 'pro_yearly',
    name: 'Pro (Annual)',
    price: 99.99,
    interval: 'year',
    features: [
      'Unlimited AI generations',
      'Advanced study modes',
      'Unlimited document uploads',
      'Advanced analytics and insights',
      'Priority support',
      'Export capabilities',
      '2 months free!'
    ]
  }
];

export const SubscriptionManagement: React.FC = () => {
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isChanging, setIsChanging] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchSubscriptionData();
  }, []);

  const fetchSubscriptionData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/subscription', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch subscription data');
      }

      const result = await response.json();
      if (result.success) {
        setSubscriptionData(result.data);
      } else {
        throw new Error(result.error);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load subscription data');
      // Set mock data for development
      setSubscriptionData({
        currentPlan: subscriptionPlans[0], // Free plan
        status: 'active',
        nextBillingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handlePlanChange = async (planId: string) => {
    setIsChanging(true);
    setError(null);

    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/subscription/change', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ planId }),
      });

      if (!response.ok) {
        throw new Error('Failed to change subscription plan');
      }

      const result = await response.json();
      if (result.success) {
        await fetchSubscriptionData();
      } else {
        throw new Error(result.error);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to change subscription plan');
    } finally {
      setIsChanging(false);
    }
  };

  const handleCancelSubscription = async () => {
    if (!confirm('Are you sure you want to cancel your subscription? You will lose access to Pro features at the end of your billing period.')) {
      return;
    }

    setIsChanging(true);
    setError(null);

    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/subscription/cancel', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to cancel subscription');
      }

      const result = await response.json();
      if (result.success) {
        await fetchSubscriptionData();
      } else {
        throw new Error(result.error);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to cancel subscription');
    } finally {
      setIsChanging(false);
    }
  };

  const handleReactivateSubscription = async () => {
    setIsChanging(true);
    setError(null);

    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/subscription/reactivate', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to reactivate subscription');
      }

      const result = await response.json();
      if (result.success) {
        await fetchSubscriptionData();
      } else {
        throw new Error(result.error);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to reactivate subscription');
    } finally {
      setIsChanging(false);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 w-48 bg-gray-600 rounded mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-64 bg-gray-600 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">Subscription Management</h3>
        
        {error && (
          <div className="mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <HiExclamationCircle className="w-5 h-5 text-red-400" />
              <span className="text-red-400 font-medium">Error</span>
            </div>
            <p className="text-red-300 mt-1">{error}</p>
          </div>
        )}

        {/* Current Subscription Status */}
        {subscriptionData && (
          <div className="bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h4 className="text-lg font-medium text-white">Current Plan</h4>
                <p className="text-gray-400">
                  {subscriptionData.currentPlan?.name || 'No active plan'}
                </p>
              </div>
              <div className="text-right">
                <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                  subscriptionData.status === 'active' ? 'bg-green-500/20 text-green-400' :
                  subscriptionData.status === 'canceled' ? 'bg-red-500/20 text-red-400' :
                  subscriptionData.status === 'past_due' ? 'bg-yellow-500/20 text-yellow-400' :
                  'bg-blue-500/20 text-blue-400'
                }`}>
                  {subscriptionData.status.charAt(0).toUpperCase() + subscriptionData.status.slice(1)}
                </div>
              </div>
            </div>

            {subscriptionData.nextBillingDate && (
              <div className="flex items-center space-x-2 text-gray-400 text-sm">
                <HiInformationCircle className="w-4 h-4" />
                <span>
                  {subscriptionData.cancelAtPeriodEnd 
                    ? `Access ends on ${new Date(subscriptionData.nextBillingDate).toLocaleDateString()}`
                    : `Next billing date: ${new Date(subscriptionData.nextBillingDate).toLocaleDateString()}`
                  }
                </span>
              </div>
            )}

            {subscriptionData.currentPlan?.id !== 'free' && (
              <div className="mt-4 flex space-x-3">
                {subscriptionData.cancelAtPeriodEnd ? (
                  <Button
                    onClick={handleReactivateSubscription}
                    isLoading={isChanging}
                    variant="primary"
                    size="sm"
                  >
                    Reactivate Subscription
                  </Button>
                ) : (
                  <Button
                    onClick={handleCancelSubscription}
                    isLoading={isChanging}
                    variant="danger"
                    size="sm"
                  >
                    Cancel Subscription
                  </Button>
                )}
                <Button
                  onClick={fetchSubscriptionData}
                  variant="secondary"
                  size="sm"
                >
                  <HiRefresh className="w-4 h-4 mr-2" />
                  Refresh
                </Button>
              </div>
            )}
          </div>
        )}

        {/* Available Plans */}
        <div>
          <h4 className="text-lg font-medium text-white mb-4">Available Plans</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {subscriptionPlans.map((plan) => {
              const isCurrent = subscriptionData?.currentPlan?.id === plan.id;
              
              return (
                <motion.div
                  key={plan.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  className={`relative bg-background-secondary rounded-lg p-6 border transition-all duration-200 ${
                    plan.popular 
                      ? 'border-primary-500 ring-2 ring-primary-500/20' 
                      : isCurrent
                        ? 'border-green-500 ring-2 ring-green-500/20'
                        : 'border-border-primary hover:border-gray-500'
                  }`}
                >
                  {plan.popular && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <div className="bg-primary-500 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center space-x-1">
                        <HiStar className="w-3 h-3" />
                        <span>Most Popular</span>
                      </div>
                    </div>
                  )}

                  {isCurrent && (
                    <div className="absolute -top-3 right-4">
                      <div className="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center space-x-1">
                        <HiCheck className="w-3 h-3" />
                        <span>Current</span>
                      </div>
                    </div>
                  )}

                  <div className="text-center mb-6">
                    <h5 className="text-xl font-semibold text-white mb-2">{plan.name}</h5>
                    <div className="text-3xl font-bold text-white">
                      ${plan.price}
                      <span className="text-lg text-gray-400">/{plan.interval}</span>
                    </div>
                  </div>

                  <ul className="space-y-3 mb-6">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-center space-x-2">
                        <HiCheck className="w-4 h-4 text-green-400 flex-shrink-0" />
                        <span className="text-gray-300 text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <Button
                    onClick={() => handlePlanChange(plan.id)}
                    disabled={isCurrent || isChanging}
                    isLoading={isChanging}
                    variant={plan.popular ? 'primary' : 'secondary'}
                    className="w-full"
                  >
                    {isCurrent ? 'Current Plan' : `Switch to ${plan.name}`}
                  </Button>
                </motion.div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};
