import{c as _,j as e,m as f,C as v,f as k,l as L,k as T,r as N,D as P,B as w,y as D,E as $,F as B,G as U,o as H,J as F,q as M,b as O,z,A as R,p as V,K as W}from"./index-fc7af858.js";const E=_((a,o)=>({balance:0,transactions:[],operationCosts:[],stats:null,isLoading:!1,error:null,fetchBalance:async()=>{a({isLoading:!0,error:null});try{const i=await fetch("/api/credits/balance",{headers:{Authorization:`Bearer ${localStorage.getItem("token")||sessionStorage.getItem("token")}`}});if(!i.ok)throw new Error("Failed to fetch credit balance");const t=await i.json();if(t.success)a({balance:t.data.credits,isLoading:!1});else throw new Error(t.error||"Failed to fetch balance")}catch(i){a({error:i instanceof Error?i.message:"Unknown error",isLoading:!1})}},fetchTransactions:async(i=50,t=0)=>{a({isLoading:!0,error:null});try{const d=await fetch(`/api/credits/history?limit=${i}&offset=${t}`,{headers:{Authorization:`Bearer ${localStorage.getItem("token")||sessionStorage.getItem("token")}`}});if(!d.ok)throw new Error("Failed to fetch credit history");const h=await d.json();if(h.success)a({transactions:t===0?h.data:[...o().transactions,...h.data],isLoading:!1});else throw new Error(h.error||"Failed to fetch transactions")}catch(d){a({error:d instanceof Error?d.message:"Unknown error",isLoading:!1})}},fetchOperationCosts:async()=>{a({isLoading:!0,error:null});try{const i=await fetch("/api/credits/pricing",{headers:{Authorization:`Bearer ${localStorage.getItem("token")||sessionStorage.getItem("token")}`}});if(!i.ok)throw new Error("Failed to fetch operation costs");const t=await i.json();if(t.success)a({operationCosts:t.data,isLoading:!1});else throw new Error(t.error||"Failed to fetch operation costs")}catch(i){a({error:i instanceof Error?i.message:"Unknown error",isLoading:!1})}},fetchStats:async(i=30)=>{a({isLoading:!0,error:null});try{const t=await fetch(`/api/credits/stats?days=${i}`,{headers:{Authorization:`Bearer ${localStorage.getItem("token")||sessionStorage.getItem("token")}`}});if(!t.ok)throw new Error("Failed to fetch credit stats");const d=await t.json();if(d.success)a({stats:d.data,isLoading:!1});else throw new Error(d.error||"Failed to fetch stats")}catch(t){a({error:t instanceof Error?t.message:"Unknown error",isLoading:!1})}},purchaseCredits:async i=>{a({isLoading:!0,error:null});try{const t=await fetch("/api/credits/purchase",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")||sessionStorage.getItem("token")}`},body:JSON.stringify({amount:i})});if(!t.ok)throw new Error("Failed to initiate credit purchase");const d=await t.json();if(d.success)return await o().fetchBalance(),await o().fetchTransactions(),a({isLoading:!1}),{success:!0};throw new Error(d.error||"Failed to purchase credits")}catch(t){const d=t instanceof Error?t.message:"Unknown error";return a({error:d,isLoading:!1}),{success:!1,error:d}}},clearError:()=>a({error:null})})),q=({balance:a,userTier:o,isLoading:i})=>{const t=x=>{switch(x.toLowerCase()){case"pro":return"text-purple-400";case"basic":return"text-blue-400";default:return"text-gray-400"}},d=x=>{switch(x.toLowerCase()){case"pro":return e.jsx(T,{className:"w-5 h-5"});case"basic":return e.jsx(L,{className:"w-5 h-5"});default:return e.jsx(v,{className:"w-5 h-5"})}},m=(x=>x>=100?{color:"text-green-400",status:"Excellent"}:x>=50?{color:"text-yellow-400",status:"Good"}:x>=10?{color:"text-orange-400",status:"Low"}:{color:"text-red-400",status:"Critical"})(a);return e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs(f.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"md:col-span-2 bg-gradient-to-br from-primary-500/20 to-purple-600/20 rounded-lg p-6 border border-primary-500/30",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"p-3 bg-primary-500/20 rounded-lg",children:e.jsx(v,{className:"w-6 h-6 text-primary-400"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Credit Balance"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Available for AI generation"})]})]}),e.jsxs("div",{className:`flex items-center space-x-2 ${t(o)}`,children:[d(o),e.jsxs("span",{className:"font-medium",children:[o," Plan"]})]})]}),e.jsxs("div",{className:"flex items-end space-x-4",children:[e.jsx("div",{children:i?e.jsx("div",{className:"animate-pulse",children:e.jsx("div",{className:"h-12 w-32 bg-gray-600 rounded"})}):e.jsxs(f.div,{initial:{scale:.8},animate:{scale:1},transition:{duration:.3,delay:.2},children:[e.jsx("span",{className:"text-4xl font-bold text-white",children:a.toLocaleString()}),e.jsx("span",{className:"text-xl text-gray-400 ml-2",children:"credits"})]})}),e.jsxs("div",{className:`flex items-center space-x-1 ${m.color} mb-2`,children:[e.jsx("div",{className:`w-2 h-2 rounded-full ${m.color.replace("text-","bg-")}`}),e.jsx("span",{className:"text-sm font-medium",children:m.status})]})]}),a<10&&e.jsxs(f.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},className:"mt-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(k,{className:"w-4 h-4 text-red-400"}),e.jsx("span",{className:"text-red-400 text-sm font-medium",children:"Low Balance Warning"})]}),e.jsx("p",{className:"text-red-300 text-sm mt-1",children:"Consider purchasing more credits to continue using AI features."})]})]}),e.jsxs(f.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1},className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsx("h4",{className:"text-lg font-semibold text-white mb-4",children:"Quick Stats"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-400 text-sm",children:"Plan Type"}),e.jsx("span",{className:`font-medium ${t(o)}`,children:o})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-400 text-sm",children:"Status"}),e.jsx("span",{className:`font-medium ${m.color}`,children:m.status})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-400 text-sm",children:"Credits Available"}),e.jsx("span",{className:"text-white font-medium",children:a})]}),o.toLowerCase()==="free"&&e.jsx("div",{className:"pt-3 border-t border-border-secondary",children:e.jsx("p",{className:"text-gray-400 text-xs",children:"Upgrade to Basic or Pro for more credits and features"})})]})]})]})},G=({transactions:a,isLoading:o,onLoadMore:i})=>{const[t,d]=N.useState("all"),[h,m]=N.useState("date"),x=r=>r>0?e.jsx($,{className:"w-4 h-4 text-red-400"}):e.jsx(B,{className:"w-4 h-4 text-green-400"}),j=r=>r>0?"text-red-400":"text-green-400",y=r=>{const l=new Date(r);return{date:l.toLocaleDateString(),time:l.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}},s=a.filter(r=>t==="used"?r.credits_used>0:t==="purchased"?r.credits_used<0:!0),n=[...s].sort((r,l)=>h==="date"?new Date(l.created_at).getTime()-new Date(r.created_at).getTime():Math.abs(l.credits_used)-Math.abs(r.credits_used)),c=()=>{const r=[["Date","Type","Credits","Operation","Description"].join(","),...n.map(g=>[new Date(g.created_at).toLocaleDateString(),g.credits_used>0?"Used":"Purchased",Math.abs(g.credits_used),g.operation_type,`"${g.description}"`].join(","))].join(`
`),l=new Blob([r],{type:"text/csv"}),p=window.URL.createObjectURL(l),b=document.createElement("a");b.href=p,b.download=`credit-history-${new Date().toISOString().split("T")[0]}.csv`,b.click(),window.URL.revokeObjectURL(p)};return e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 space-y-4 sm:space-y-0",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Transaction History"}),e.jsxs("p",{className:"text-gray-400 text-sm",children:[s.length," of ",a.length," transactions"]})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("div",{className:"relative",children:[e.jsxs("select",{value:t,onChange:r=>d(r.target.value),className:"appearance-none bg-background-tertiary border border-border-secondary rounded-lg px-3 py-2 pr-8 text-white text-sm focus:outline-none focus:ring-2 focus:ring-primary-500",children:[e.jsx("option",{value:"all",children:"All Transactions"}),e.jsx("option",{value:"used",children:"Credits Used"}),e.jsx("option",{value:"purchased",children:"Credits Purchased"})]}),e.jsx(P,{className:"absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none"})]}),e.jsxs("div",{className:"relative",children:[e.jsxs("select",{value:h,onChange:r=>m(r.target.value),className:"appearance-none bg-background-tertiary border border-border-secondary rounded-lg px-3 py-2 pr-8 text-white text-sm focus:outline-none focus:ring-2 focus:ring-primary-500",children:[e.jsx("option",{value:"date",children:"Sort by Date"}),e.jsx("option",{value:"amount",children:"Sort by Amount"})]}),e.jsx(P,{className:"absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none"})]}),e.jsxs(w,{onClick:c,variant:"secondary",size:"sm",disabled:a.length===0,children:[e.jsx(D,{className:"w-4 h-4 mr-2"}),"Export"]})]})]}),e.jsx("div",{className:"space-y-3",children:o&&a.length===0?e.jsx("div",{className:"space-y-3",children:[...Array(5)].map((r,l)=>e.jsx("div",{className:"animate-pulse",children:e.jsx("div",{className:"bg-background-tertiary rounded-lg p-4",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"w-8 h-8 bg-gray-600 rounded-full"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"h-4 bg-gray-600 rounded w-1/3 mb-2"}),e.jsx("div",{className:"h-3 bg-gray-600 rounded w-1/2"})]}),e.jsx("div",{className:"h-6 bg-gray-600 rounded w-16"})]})})},l))}):n.length===0?e.jsxs("div",{className:"text-center py-12",children:[e.jsx(k,{className:"w-12 h-12 text-gray-500 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-400 mb-2",children:"No Transactions Found"}),e.jsx("p",{className:"text-gray-500",children:t==="all"?"You haven't made any credit transactions yet.":`No ${t} transactions found.`})]}):n.map((r,l)=>{const{date:p,time:b}=y(r.created_at),g=r.credits_used<0;return e.jsx(f.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:l*.05},className:"bg-background-tertiary rounded-lg p-4 border border-border-secondary hover:border-border-primary transition-colors",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:`p-2 rounded-full ${g?"bg-green-500/20":"bg-red-500/20"}`,children:x(r.credits_used)}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-white",children:r.operation_type.replace(/_/g," ").replace(/\b\w/g,C=>C.toUpperCase())}),e.jsx("p",{className:"text-gray-400 text-sm",children:r.description}),e.jsxs("div",{className:"flex items-center space-x-2 mt-1",children:[e.jsx("span",{className:"text-gray-500 text-xs",children:p}),e.jsx("span",{className:"text-gray-600",children:"•"}),e.jsx("span",{className:"text-gray-500 text-xs",children:b})]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("span",{className:`font-semibold ${j(r.credits_used)}`,children:[g?"+":"-",Math.abs(r.credits_used)," credits"]}),r.study_set_id&&e.jsx("p",{className:"text-gray-500 text-xs mt-1",children:"Study Set"})]})]})},r.id)})}),a.length>0&&a.length%50===0&&e.jsx("div",{className:"mt-6 text-center",children:e.jsx(w,{onClick:i,variant:"secondary",isLoading:o,disabled:o,children:"Load More Transactions"})})]})},A=[{id:"starter",name:"Starter Pack",credits:100,price:9.99,description:"Perfect for trying out AI features",features:["100 AI generation credits","Valid for 6 months","All AI features included"]},{id:"popular",name:"Popular Pack",credits:500,price:39.99,bonus:50,popular:!0,description:"Most popular choice for regular users",features:["500 AI generation credits","+50 bonus credits","Valid for 12 months","Priority support","All AI features included"]},{id:"power",name:"Power User",credits:1e3,price:69.99,bonus:200,description:"For heavy AI users and professionals",features:["1,000 AI generation credits","+200 bonus credits","Valid for 12 months","Priority support","Early access to new features","All AI features included"]},{id:"enterprise",name:"Enterprise",credits:2500,price:149.99,bonus:750,description:"Maximum value for teams and businesses",features:["2,500 AI generation credits","+750 bonus credits","Valid for 18 months","Dedicated support","Early access to new features","Custom integrations available","All AI features included"]}],J=({currentBalance:a,onPurchaseComplete:o})=>{const[i,t]=N.useState(null),[d,h]=N.useState(!1),{purchaseCredits:m}=E(),x=async s=>{const n=A.find(c=>c.id===s);if(n){h(!0),t(s);try{const c=await m(n.credits+(n.bonus||0));c.success?o():console.error("Purchase failed:",c.error)}catch(c){console.error("Purchase error:",c)}finally{h(!1),t(null)}}},j=s=>{switch(s){case"starter":return e.jsx(v,{className:"w-6 h-6"});case"popular":return e.jsx(F,{className:"w-6 h-6"});case"power":return e.jsx(H,{className:"w-6 h-6"});case"enterprise":return e.jsx(T,{className:"w-6 h-6"});default:return e.jsx(v,{className:"w-6 h-6"})}},y=(s,n,c)=>{if(!n)return null;const l=(s+n)/s*c,p=(l-c)/l*100;return Math.round(p)};return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Current Balance"}),e.jsx("p",{className:"text-gray-400",children:"Your available credits"})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("span",{className:"text-2xl font-bold text-primary-400",children:a}),e.jsx("span",{className:"text-gray-400 ml-2",children:"credits"})]})]})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-6",children:"Choose a Credit Package"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:A.map((s,n)=>{const c=s.credits+(s.bonus||0),r=s.bonus?y(s.credits,s.bonus,s.price):null,l=i===s.id,p=d&&l;return e.jsxs(f.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:n*.1},className:`
                  relative bg-background-secondary rounded-lg p-6 border transition-all duration-200
                  ${s.popular?"border-primary-500 ring-2 ring-primary-500/20":"border-border-primary hover:border-border-secondary"}
                  ${l?"ring-2 ring-primary-500/50":""}
                `,children:[s.popular&&e.jsx("div",{className:"absolute -top-3 left-1/2 transform -translate-x-1/2",children:e.jsx("div",{className:"bg-primary-500 text-white px-3 py-1 rounded-full text-xs font-medium",children:"Most Popular"})}),r&&e.jsx("div",{className:"absolute -top-2 -right-2",children:e.jsxs("div",{className:"bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium",children:[r,"% OFF"]})}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:`inline-flex p-3 rounded-lg mb-4 ${s.popular?"bg-primary-500/20 text-primary-400":"bg-background-tertiary text-gray-400"}`,children:j(s.id)}),e.jsx("h4",{className:"text-lg font-semibold text-white mb-2",children:s.name}),e.jsxs("div",{className:"mb-4",children:[e.jsx("span",{className:"text-3xl font-bold text-white",children:s.credits}),s.bonus&&e.jsxs("span",{className:"text-green-400 text-sm ml-1",children:["+",s.bonus]}),e.jsx("div",{className:"text-gray-400 text-sm",children:"credits"}),s.bonus&&e.jsxs("div",{className:"text-green-400 text-xs",children:["Total: ",c," credits"]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("span",{className:"text-2xl font-bold text-white",children:["$",s.price]}),e.jsxs("div",{className:"text-gray-400 text-sm",children:["$",(s.price/c).toFixed(3)," per credit"]})]}),e.jsx("p",{className:"text-gray-400 text-sm mb-4",children:s.description}),e.jsx("div",{className:"space-y-2 mb-6",children:s.features.map((b,g)=>e.jsxs("div",{className:"flex items-center text-sm text-gray-300",children:[e.jsx(U,{className:"w-4 h-4 text-green-400 mr-2 flex-shrink-0"}),e.jsx("span",{children:b})]},g))}),e.jsx(w,{onClick:()=>x(s.id),variant:s.popular?"primary":"secondary",className:"w-full",isLoading:p,disabled:d,children:p?"Processing...":`Purchase ${s.name}`})]})]},s.id)})})]}),e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsx("h4",{className:"text-lg font-semibold text-white mb-4",children:"Payment Information"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-white mb-2",children:"Secure Payment"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"All payments are processed securely through Stripe. We never store your payment information."})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-white mb-2",children:"Credit Expiration"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Credits are valid for the duration specified in each package. Unused credits will expire after the validity period."})]})]})]})]})},Y=({stats:a,operationCosts:o})=>{const i=N.useMemo(()=>{const s=Array.from({length:7},(n,c)=>{const r=new Date;return r.setDate(r.getDate()-(6-c)),{date:r.toLocaleDateString("en-US",{weekday:"short"}),fullDate:r.toISOString().split("T")[0],credits:0}});return a.dailyUsage.forEach(n=>{const c=s.findIndex(r=>r.fullDate===n.date);c!==-1&&(s[c].credits=n.credits)}),s},[a.dailyUsage]),t=Math.max(...i.map(s=>s.credits),1),d=i.reduce((s,n)=>s+n.credits,0),h=d/7,m=i.slice(0,3).reduce((s,n)=>s+n.credits,0)/3,x=i.slice(4).reduce((s,n)=>s+n.credits,0)/3,j=x>m?"up":x<m?"down":"stable",y=m>0?Math.abs((x-m)/m*100):0;return e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Usage Trends"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Last 7 days"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[j==="up"?e.jsx(L,{className:"w-5 h-5 text-green-400"}):j==="down"?e.jsx(M,{className:"w-5 h-5 text-red-400"}):null,j!=="stable"&&e.jsxs("span",{className:`text-sm font-medium ${j==="up"?"text-green-400":"text-red-400"}`,children:[y.toFixed(1),"%"]})]})]}),e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"flex items-end justify-between h-32 space-x-2",children:i.map((s,n)=>{const c=t>0?s.credits/t*100:0;return e.jsxs("div",{className:"flex-1 flex flex-col items-center",children:[e.jsx("div",{className:"w-full flex justify-center mb-2",children:e.jsx(f.div,{initial:{height:0},animate:{height:`${c}%`},transition:{duration:.5,delay:n*.1},className:"w-full max-w-8 bg-gradient-to-t from-primary-500 to-primary-400 rounded-t-sm relative group",style:{minHeight:c>0?"4px":"0px"},children:e.jsx("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity",children:e.jsxs("div",{className:"bg-background-tertiary border border-border-primary rounded px-2 py-1 text-xs text-white whitespace-nowrap",children:[s.credits," credits"]})})})}),e.jsx("span",{className:"text-xs text-gray-400",children:s.date})]},s.date)})})}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx("div",{className:"bg-background-tertiary rounded-lg p-4",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-white",children:d}),e.jsx("div",{className:"text-gray-400 text-sm",children:"Total This Week"})]})}),e.jsx("div",{className:"bg-background-tertiary rounded-lg p-4",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-white",children:h.toFixed(1)}),e.jsx("div",{className:"text-gray-400 text-sm",children:"Daily Average"})]})})]}),e.jsxs("div",{className:"mt-4 pt-4 border-t border-border-secondary",children:[e.jsx("h4",{className:"text-sm font-medium text-white mb-3",children:"Usage Efficiency"}),e.jsx("div",{className:"space-y-2",children:Object.entries(a.usageByOperation).slice(0,3).map(([s,n])=>{const c=o.find(l=>l.operation_type===s),r=c?Math.floor(n/c.credits_required):0;return e.jsxs("div",{className:"flex justify-between items-center text-sm",children:[e.jsx("span",{className:"text-gray-300 capitalize",children:s.replace(/_/g," ")}),e.jsxs("span",{className:"text-white",children:[r," generations"]})]},s)})})]})]})},K=[{id:"overview",label:"Overview",icon:V,description:"Credit balance and usage summary"},{id:"history",label:"Transaction History",icon:k,description:"Detailed credit transaction log"},{id:"purchase",label:"Buy Credits",icon:W,description:"Purchase additional credits"}],X=()=>{const[a,o]=N.useState("overview"),{user:i}=O(),{balance:t,transactions:d,operationCosts:h,stats:m,isLoading:x,error:j,fetchBalance:y,fetchTransactions:s,fetchOperationCosts:n,fetchStats:c,clearError:r}=E();N.useEffect(()=>{y(),s(),n(),c()},[y,s,n,c]);const l=async()=>{r(),await Promise.all([y(),s(),n(),c()])},p=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsx(q,{balance:t,userTier:(i==null?void 0:i.subscription_tier)||"Free",isLoading:x}),m&&e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsx(Y,{stats:m,operationCosts:h}),e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Usage Breakdown"}),e.jsx("div",{className:"space-y-3",children:Object.entries(m.usageByOperation).map(([u,S])=>e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-300 capitalize",children:u.replace(/_/g," ")}),e.jsxs("span",{className:"text-white font-medium",children:[S," credits"]})]},u))})]})]}),e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Credit Costs"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:h.map(u=>e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-4 border border-border-secondary",children:[e.jsx("h4",{className:"font-medium text-white capitalize mb-2",children:u.operation_type.replace(/_/g," ")}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(v,{className:"w-4 h-4 text-primary-400"}),e.jsxs("span",{className:"text-primary-400 font-semibold",children:[u.credits_required," credits"]})]})]},u.operation_type))})]})]}),b=()=>e.jsx(G,{transactions:d,isLoading:x,onLoadMore:()=>s(50,d.length)}),g=()=>e.jsx(J,{currentBalance:t,userTier:(i==null?void 0:i.subscription_tier)||"Free",onPurchaseComplete:l}),C=()=>{switch(a){case"overview":return p();case"history":return b();case"purchase":return g();default:return p()}};return e.jsx("div",{className:"min-h-screen bg-background-primary text-white",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-8",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Credits"}),e.jsx("p",{className:"text-gray-400",children:"Manage your AI generation credits"})]}),e.jsx("div",{className:"flex items-center space-x-4",children:e.jsxs(w,{onClick:l,variant:"secondary",disabled:x,children:[e.jsx(z,{className:`w-4 h-4 mr-2 ${x?"animate-spin":""}`}),"Refresh"]})})]}),j&&e.jsxs("div",{className:"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(R,{className:"w-5 h-5 text-red-400"}),e.jsx("span",{className:"text-red-400 font-medium",children:"Error"})]}),e.jsx("p",{className:"text-red-300 mt-1",children:j}),e.jsx(w,{onClick:r,variant:"secondary",size:"sm",className:"mt-2",children:"Dismiss"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[e.jsx("div",{className:"lg:col-span-1",children:e.jsx("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:e.jsx("nav",{className:"space-y-2",children:K.map(u=>{const S=u.icon,I=a===u.id;return e.jsxs("button",{onClick:()=>o(u.id),className:`
                        w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left
                        transition-all duration-200
                        ${I?"bg-primary-500/20 text-primary-400 border border-primary-500/30":"text-gray-300 hover:bg-background-tertiary hover:text-white"}
                      `,children:[e.jsx(S,{className:"w-5 h-5"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("span",{className:"font-medium block",children:u.label}),e.jsx("span",{className:"text-xs text-gray-500 block truncate",children:u.description})]})]},u.id)})})})}),e.jsx("div",{className:"lg:col-span-3",children:e.jsx(f.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.3},children:C()},a)})]})]})})};export{X as CreditsPage};
