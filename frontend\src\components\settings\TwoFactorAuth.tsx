import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  HiExclamationCircle,
  <PERSON>CheckCircle,
  HiClipboard,
  Hi<PERSON>ye,
  HiEyeOff
} from 'react-icons/hi';
import { Button } from '../common/Button';
import { Input } from '../common/Input';

interface TwoFactorAuthProps {
  enabled: boolean;
  onToggle: (enabled: boolean) => void;
}

interface SetupData {
  qrCode: string;
  secret: string;
  backupCodes: string[];
}

export const TwoFactorAuth: React.FC<TwoFactorAuthProps> = ({ enabled, onToggle }) => {
  const [isSetupMode, setIsSetupMode] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [setupData, setSetupData] = useState<SetupData | null>(null);
  const [verificationCode, setVerificationCode] = useState('');
  const [showSecret, setShowSecret] = useState(false);
  const [showBackupCodes, setShowBackupCodes] = useState(false);

  const handleEnable2FA = async () => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/auth/2fa/setup', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to setup 2FA');
      }

      const result = await response.json();
      if (result.success) {
        setSetupData(result.data);
        setIsSetupMode(true);
      } else {
        throw new Error(result.error);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to setup 2FA');
      // Mock data for development
      setSetupData({
        qrCode: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        secret: 'JBSWY3DPEHPK3PXP',
        backupCodes: [
          '12345678',
          '87654321',
          '11111111',
          '22222222',
          '33333333',
          '44444444',
          '55555555',
          '66666666'
        ]
      });
      setIsSetupMode(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerify2FA = async () => {
    if (!verificationCode || verificationCode.length !== 6) {
      setError('Please enter a valid 6-digit code');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/auth/2fa/verify', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ code: verificationCode }),
      });

      if (!response.ok) {
        throw new Error('Failed to verify 2FA code');
      }

      const result = await response.json();
      if (result.success) {
        setSuccess('Two-factor authentication enabled successfully!');
        setIsSetupMode(false);
        setShowBackupCodes(true);
        onToggle(true);
      } else {
        throw new Error(result.error || 'Invalid verification code');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to verify 2FA code');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDisable2FA = async () => {
    if (!confirm('Are you sure you want to disable two-factor authentication? This will make your account less secure.')) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/auth/2fa/disable', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to disable 2FA');
      }

      const result = await response.json();
      if (result.success) {
        setSuccess('Two-factor authentication disabled successfully');
        onToggle(false);
      } else {
        throw new Error(result.error);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to disable 2FA');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setSuccess('Copied to clipboard!');
    setTimeout(() => setSuccess(null), 2000);
  };

  const handleCancelSetup = () => {
    setIsSetupMode(false);
    setSetupData(null);
    setVerificationCode('');
    setError(null);
    setSuccess(null);
  };

  if (isSetupMode && setupData) {
    return (
      <div className="bg-background-tertiary rounded-lg p-6 border border-border-primary">
        <div className="flex items-center space-x-3 mb-6">
          <HiShieldCheck className="w-6 h-6 text-primary-400" />
          <h4 className="text-lg font-medium text-white">Setup Two-Factor Authentication</h4>
        </div>

        {error && (
          <div className="mb-4 bg-red-500/20 border border-red-500/30 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <HiExclamationCircle className="w-4 h-4 text-red-400" />
              <span className="text-red-400 text-sm">{error}</span>
            </div>
          </div>
        )}

        {success && (
          <div className="mb-4 bg-green-500/20 border border-green-500/30 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <HiCheckCircle className="w-4 h-4 text-green-400" />
              <span className="text-green-400 text-sm">{success}</span>
            </div>
          </div>
        )}

        {!showBackupCodes ? (
          <div className="space-y-6">
            <div className="text-center">
              <h5 className="font-medium text-white mb-2">Step 1: Scan QR Code</h5>
              <p className="text-gray-400 text-sm mb-4">
                Scan this QR code with your authenticator app (Google Authenticator, Authy, etc.)
              </p>
              <div className="bg-white p-4 rounded-lg inline-block">
                <img src={setupData.qrCode} alt="2FA QR Code" className="w-48 h-48" />
              </div>
            </div>

            <div>
              <h5 className="font-medium text-white mb-2">Step 2: Manual Entry (Alternative)</h5>
              <p className="text-gray-400 text-sm mb-3">
                If you can't scan the QR code, enter this secret key manually:
              </p>
              <div className="flex items-center space-x-2">
                <div className="flex-1 bg-background-secondary border border-border-primary rounded-lg p-3">
                  <code className="text-primary-400 font-mono">
                    {showSecret ? setupData.secret : '••••••••••••••••'}
                  </code>
                </div>
                <Button
                  onClick={() => setShowSecret(!showSecret)}
                  variant="secondary"
                  size="sm"
                >
                  {showSecret ? <HiEyeOff className="w-4 h-4" /> : <HiEye className="w-4 h-4" />}
                </Button>
                <Button
                  onClick={() => handleCopyToClipboard(setupData.secret)}
                  variant="secondary"
                  size="sm"
                >
                  <HiClipboard className="w-4 h-4" />
                </Button>
              </div>
            </div>

            <div>
              <h5 className="font-medium text-white mb-2">Step 3: Verify Setup</h5>
              <p className="text-gray-400 text-sm mb-3">
                Enter the 6-digit code from your authenticator app:
              </p>
              <div className="flex space-x-3">
                <Input
                  value={verificationCode}
                  onChange={setVerificationCode}
                  placeholder="123456"
                  className="flex-1"
                />
                <Button
                  onClick={handleVerify2FA}
                  isLoading={isLoading}
                  disabled={verificationCode.length !== 6}
                >
                  Verify
                </Button>
              </div>
            </div>

            <div className="flex space-x-3">
              <Button
                onClick={handleCancelSetup}
                variant="secondary"
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            <div className="text-center">
              <HiCheckCircle className="w-16 h-16 text-green-400 mx-auto mb-4" />
              <h5 className="text-lg font-medium text-white mb-2">2FA Enabled Successfully!</h5>
              <p className="text-gray-400 text-sm">
                Your account is now protected with two-factor authentication.
              </p>
            </div>

            <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4">
              <h5 className="font-medium text-yellow-400 mb-2">Important: Save Your Backup Codes</h5>
              <p className="text-yellow-300 text-sm mb-4">
                Store these backup codes in a safe place. You can use them to access your account if you lose your authenticator device.
              </p>
              <div className="grid grid-cols-2 gap-2 mb-4">
                {setupData.backupCodes.map((code, index) => (
                  <div key={index} className="bg-background-secondary border border-border-primary rounded p-2 text-center">
                    <code className="text-primary-400 font-mono">{code}</code>
                  </div>
                ))}
              </div>
              <Button
                onClick={() => handleCopyToClipboard(setupData.backupCodes.join('\n'))}
                variant="secondary"
                size="sm"
              >
                <HiClipboard className="w-4 h-4 mr-2" />
                Copy All Codes
              </Button>
            </div>

            <Button
              onClick={() => setShowBackupCodes(false)}
              variant="primary"
              className="w-full"
            >
              I've Saved My Backup Codes
            </Button>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="bg-background-tertiary rounded-lg p-6 border border-border-primary">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <HiShieldCheck className={`w-6 h-6 ${enabled ? 'text-green-400' : 'text-gray-400'}`} />
          <div>
            <h4 className="text-lg font-medium text-white">Two-Factor Authentication</h4>
            <p className="text-gray-400 text-sm">
              Add an extra layer of security to your account
            </p>
          </div>
        </div>
        <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
          enabled ? 'bg-green-500/20 text-green-400' : 'bg-gray-500/20 text-gray-400'
        }`}>
          {enabled ? 'Enabled' : 'Disabled'}
        </div>
      </div>

      {error && (
        <div className="mb-4 bg-red-500/20 border border-red-500/30 rounded-lg p-3">
          <div className="flex items-center space-x-2">
            <HiExclamationCircle className="w-4 h-4 text-red-400" />
            <span className="text-red-400 text-sm">{error}</span>
          </div>
        </div>
      )}

      {success && (
        <div className="mb-4 bg-green-500/20 border border-green-500/30 rounded-lg p-3">
          <div className="flex items-center space-x-2">
            <HiCheckCircle className="w-4 h-4 text-green-400" />
            <span className="text-green-400 text-sm">{success}</span>
          </div>
        </div>
      )}

      <div className="space-y-4">
        <p className="text-gray-300 text-sm">
          {enabled 
            ? 'Two-factor authentication is currently enabled for your account. You can disable it below if needed.'
            : 'Enable two-factor authentication to add an extra layer of security to your account. You\'ll need an authenticator app like Google Authenticator or Authy.'
          }
        </p>

        <div className="flex space-x-3">
          {enabled ? (
            <Button
              onClick={handleDisable2FA}
              isLoading={isLoading}
              variant="danger"
            >
              Disable 2FA
            </Button>
          ) : (
            <Button
              onClick={handleEnable2FA}
              isLoading={isLoading}
              variant="primary"
            >
              <HiKey className="w-4 h-4 mr-2" />
              Enable 2FA
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};
