import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useStudyStore } from '../stores/studyStore';
import { Button } from '../components/common/Button';
import { FlashcardManagement } from '../components/study/FlashcardManagement';
import { QuizManagement } from '../components/study/QuizManagement';
import { useDialog } from '../contexts/DialogContext';
import { useUserSettings } from '../hooks/useUserSettings';
import { ShuffleToggle } from '../components/ui/ShuffleToggle';
import { Flashcard, QuizQuestion } from '../../../shared/types';

export const StudySetPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { studySetContent, isLoading, error, fetchStudySetContent } = useStudyStore();
  const { alert, confirm, prompt } = useDialog();
  const { settings: userSettings, updateSettings } = useUserSettings();
  const [selectedMode, setSelectedMode] = useState<'flashcards' | 'quiz' | null>(null);
  const [activeTab, setActiveTab] = useState<'study' | 'manage'>('study');
  const [managementType, setManagementType] = useState<'flashcards' | 'quiz'>('flashcards');
  const [flashcards, setFlashcards] = useState<Flashcard[]>([]);
  const [questions, setQuestions] = useState<QuizQuestion[]>([]);
  const [studySetName, setStudySetName] = useState('');

  useEffect(() => {
    if (id) {
      fetchStudySetContent(id).catch(console.error);
    }
  }, [id, fetchStudySetContent]);

  useEffect(() => {
    if (studySetContent?.studySet) {
      setStudySetName(studySetContent.studySet.name);
      setFlashcards(studySetContent.flashcards || []);
      setQuestions(studySetContent.questions || []);
    }
  }, [studySetContent]);

  const handleStartStudy = async () => {
    if (!id || !selectedMode) return;

    try {
      const shuffleEnabled = userSettings?.shuffle_flashcards || false;
      await useStudyStore.getState().startStudySession(id, selectedMode, shuffleEnabled);
      navigate(`/study/${id}/${selectedMode}`);
    } catch (error: any) {
      await alert({
        title: 'Error',
        message: error.message || 'Failed to start study session',
        variant: 'error'
      });
    }
  };

  const handleRenameStudySet = async () => {
    if (!id || !studySetContent?.studySet) return;

    const newName = await prompt({
      title: 'Rename Study Set',
      message: 'Enter a new name for this study set:',
      defaultValue: studySetContent.studySet.name
    });

    if (newName === null || newName.trim() === studySetContent.studySet.name) return;

    if (!newName.trim()) {
      await alert({
        title: 'Invalid Name',
        message: 'Study set name cannot be empty.',
        variant: 'error'
      });
      return;
    }

    try {
      const response = await fetch(`/api/study-sets/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify({ name: newName.trim() })
      });

      if (!response.ok) {
        throw new Error('Failed to rename study set');
      }

      setStudySetName(newName.trim());
      await alert({
        title: 'Success',
        message: 'Study set renamed successfully!',
        variant: 'success'
      });

      // Refresh the study set content
      await fetchStudySetContent(id);
    } catch (error: any) {
      await alert({
        title: 'Error',
        message: error.message || 'Failed to rename study set',
        variant: 'error'
      });
    }
  };

  const handleDeleteStudySet = async () => {
    if (!id || !studySetContent?.studySet) return;

    const confirmed = await confirm({
      title: 'Delete Study Set',
      message: `Are you sure you want to delete "${studySetContent.studySet.name}"?\n\nThis action cannot be undone and will delete all flashcards and quiz questions in this set.`,
      variant: 'danger',
      confirmText: 'Delete Study Set',
      cancelText: 'Cancel'
    });

    if (!confirmed) return;

    try {
      const response = await fetch(`/api/study-sets/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to delete study set');
      }

      await alert({
        title: 'Success',
        message: 'Study set deleted successfully!',
        variant: 'success'
      });

      navigate('/dashboard');
    } catch (error: any) {
      await alert({
        title: 'Error',
        message: error.message || 'Failed to delete study set',
        variant: 'error'
      });
    }
  };

  const handleFlashcardAdded = (newFlashcard: Flashcard) => {
    setFlashcards(prev => [...prev, newFlashcard]);
  };

  const handleFlashcardUpdated = (updatedFlashcard: Flashcard) => {
    setFlashcards(prev => prev.map(card =>
      card.id === updatedFlashcard.id ? updatedFlashcard : card
    ));
  };

  const handleFlashcardDeleted = (flashcardId: string) => {
    setFlashcards(prev => prev.filter(card => card.id !== flashcardId));
  };

  const handleFlashcardsGenerated = (newFlashcards: Flashcard[]) => {
    setFlashcards(prev => [...prev, ...newFlashcards]);
  };

  const handleQuestionAdded = (newQuestion: QuizQuestion) => {
    setQuestions(prev => [...prev, newQuestion]);
  };

  const handleQuestionUpdated = (updatedQuestion: QuizQuestion) => {
    setQuestions(prev => prev.map(question =>
      question.id === updatedQuestion.id ? updatedQuestion : question
    ));
  };

  const handleQuestionDeleted = (questionId: string) => {
    setQuestions(prev => prev.filter(question => question.id !== questionId));
  };

  const handleQuestionsGenerated = (newQuestions: QuizQuestion[]) => {
    setQuestions(prev => [...prev, ...newQuestions]);
  };

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
          <span className="ml-3 text-gray-400">Loading study set...</span>
        </div>
      </div>
    );
  }

  if (error || !studySetContent?.studySet) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-12">
          <div className="text-red-400 mb-4">
            {error || 'Study set not found'}
          </div>
          <Button onClick={() => navigate('/dashboard')} variant="secondary">
            Back to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  const { studySet } = studySetContent;
  const hasFlashcards = flashcards && flashcards.length > 0;
  const hasQuestions = questions && questions.length > 0;

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <button
          onClick={() => navigate('/dashboard')}
          className="text-gray-400 hover:text-white mb-4 flex items-center"
        >
          ← Back to Dashboard
        </button>

        <div className="flex items-center justify-between mb-2">
          <h1 className="text-3xl font-bold text-white">{studySetName}</h1>

          <div className="flex items-center space-x-3">
            <Button
              onClick={handleRenameStudySet}
              variant="secondary"
              size="sm"
            >
              ✏️ Rename
            </Button>
            <Button
              onClick={handleDeleteStudySet}
              variant="danger"
              size="sm"
            >
              🗑️ Delete
            </Button>
          </div>
        </div>

        <div className="flex items-center space-x-4 text-sm text-gray-400">
          <span className="capitalize">{studySet.type}</span>
          {studySet.is_ai_generated && (
            <span className="bg-primary-500/20 text-primary-400 px-2 py-1 rounded">
              AI Generated
            </span>
          )}
          <span>
            Created {new Date(studySet.created_at).toLocaleDateString()}
          </span>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="mb-6">
        <div className="border-b border-gray-600">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('study')}
              className={`
                py-2 px-1 border-b-2 font-medium text-sm transition-colors
                ${activeTab === 'study'
                  ? 'border-primary-500 text-primary-400'
                  : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                }
              `}
            >
              📚 Study Mode
            </button>
            <button
              onClick={() => setActiveTab('manage')}
              className={`
                py-2 px-1 border-b-2 font-medium text-sm transition-colors
                ${activeTab === 'manage'
                  ? 'border-primary-500 text-primary-400'
                  : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                }
              `}
            >
              ⚙️ Manage Content
            </button>
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'study' && (
        <>
          {/* Study Mode Selection */}
          <div className="bg-background-secondary rounded-lg p-6 mb-6">
            <h2 className="text-xl font-semibold text-white mb-4">Choose Study Mode</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              {/* Flashcard Mode */}
              {hasFlashcards && (
                <div
                  className={`
                    p-4 rounded-lg border-2 cursor-pointer transition-all
                    ${selectedMode === 'flashcards'
                      ? 'border-primary-500 bg-primary-500/10'
                      : 'border-gray-600 hover:border-gray-500'
                    }
                  `}
                  onClick={() => setSelectedMode('flashcards')}
                >
                  <div className="flex items-center space-x-3">
                    <div className="text-2xl">🃏</div>
                    <div>
                      <h3 className="font-medium text-white">Flashcard Review</h3>
                      <p className="text-sm text-gray-400">
                        {flashcards?.length} flashcards • Interactive review
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Quiz Mode */}
              {hasQuestions && (
                <div
                  className={`
                    p-4 rounded-lg border-2 cursor-pointer transition-all
                    ${selectedMode === 'quiz'
                      ? 'border-primary-500 bg-primary-500/10'
                      : 'border-gray-600 hover:border-gray-500'
                    }
                  `}
                  onClick={() => setSelectedMode('quiz')}
                >
                  <div className="flex items-center space-x-3">
                    <div className="text-2xl">📝</div>
                    <div>
                      <h3 className="font-medium text-white">Quiz Practice</h3>
                      <p className="text-sm text-gray-400">
                        {questions?.length} questions • Test your knowledge
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Shuffle Toggle */}
            {selectedMode && userSettings && (
              <div className="mb-6 p-4 bg-background-tertiary rounded-lg border border-gray-600">
                <ShuffleToggle
                  enabled={userSettings.shuffle_flashcards}
                  onChange={async (enabled) => {
                    try {
                      await updateSettings({ shuffle_flashcards: enabled });
                    } catch (error) {
                      console.error('Failed to update shuffle setting:', error);
                    }
                  }}
                  label="Shuffle Cards"
                  description="Randomize the order of flashcards during study sessions"
                />
              </div>
            )}

            {/* Start Button */}
            <Button
              onClick={handleStartStudy}
              disabled={!selectedMode}
              className="w-full"
              size="lg"
            >
              {selectedMode
                ? `Start ${selectedMode === 'flashcards' ? 'Flashcard Review' : 'Quiz Practice'}`
                : 'Select a study mode'
              }
            </Button>
          </div>
        </>
      )}

      {activeTab === 'manage' && id && (
        <div className="bg-background-secondary rounded-lg p-6 mb-6">
          {/* Management Type Tabs */}
          <div className="flex space-x-1 mb-6">
            <button
              onClick={() => setManagementType('flashcards')}
              className={`
                py-2 px-4 rounded-lg font-medium text-sm transition-colors
                ${managementType === 'flashcards'
                  ? 'bg-primary-500 text-white'
                  : 'bg-background-primary text-gray-400 hover:text-gray-300'
                }
              `}
            >
              📚 Flashcards ({flashcards.length})
            </button>
            <button
              onClick={() => setManagementType('quiz')}
              className={`
                py-2 px-4 rounded-lg font-medium text-sm transition-colors
                ${managementType === 'quiz'
                  ? 'bg-primary-500 text-white'
                  : 'bg-background-primary text-gray-400 hover:text-gray-300'
                }
              `}
            >
              ❓ Quiz Questions ({questions.length})
            </button>
          </div>

          {/* Management Content */}
          {managementType === 'flashcards' && (
            <FlashcardManagement
              studySetId={id}
              flashcards={flashcards}
              onFlashcardAdded={handleFlashcardAdded}
              onFlashcardUpdated={handleFlashcardUpdated}
              onFlashcardDeleted={handleFlashcardDeleted}
              onFlashcardsGenerated={handleFlashcardsGenerated}
            />
          )}

          {managementType === 'quiz' && (
            <QuizManagement
              studySetId={id}
              questions={questions}
              onQuestionAdded={handleQuestionAdded}
              onQuestionUpdated={handleQuestionUpdated}
              onQuestionDeleted={handleQuestionDeleted}
              onQuestionsGenerated={handleQuestionsGenerated}
            />
          )}
        </div>
      )}

      {/* Study Set Info */}
      <div className="bg-background-secondary rounded-lg p-6">
        <h3 className="text-lg font-medium text-white mb-4">Study Set Details</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="text-sm font-medium text-gray-300 mb-2">Content</h4>
            <div className="space-y-1 text-sm text-gray-400">
              {hasFlashcards && (
                <div>{flashcards.length} flashcards</div>
              )}
              {hasQuestions && (
                <div>{questions?.length} quiz questions</div>
              )}
              {!hasFlashcards && !hasQuestions && (
                <div className="text-gray-500">No content yet</div>
              )}
            </div>
          </div>

          {studySet.source_documents && studySet.source_documents.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-300 mb-2">Source Documents</h4>
              <div className="space-y-1 text-sm text-gray-400">
                {studySet.source_documents.map((doc, index) => (
                  <div key={index}>{doc.filename}</div>
                ))}
              </div>
            </div>
          )}

          {studySet.custom_prompt && (
            <div className="md:col-span-2">
              <h4 className="text-sm font-medium text-gray-300 mb-2">Custom Instructions</h4>
              <p className="text-sm text-gray-400">{studySet.custom_prompt}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
