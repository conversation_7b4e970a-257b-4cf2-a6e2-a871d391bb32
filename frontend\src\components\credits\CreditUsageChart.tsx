import React, { useMemo } from 'react';
import { motion } from 'framer-motion';
import { HiTrendingUp, HiTrendingDown } from 'react-icons/hi';
import { AIOperationCost } from '../../../../shared/types';

interface CreditStats {
  totalUsed: number;
  totalPurchased: number;
  usageByOperation: Record<string, number>;
  dailyUsage: Array<{ date: string; credits: number }>;
}

interface CreditUsageChartProps {
  stats: CreditStats;
  operationCosts: AIOperationCost[];
}

export const CreditUsageChart: React.FC<CreditUsageChartProps> = ({
  stats,
  operationCosts
}) => {
  const chartData = useMemo(() => {
    // Get last 7 days of usage data
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (6 - i));
      return {
        date: date.toLocaleDateString('en-US', { weekday: 'short' }),
        fullDate: date.toISOString().split('T')[0],
        credits: 0
      };
    });

    // Map actual usage data to the 7-day structure
    stats.dailyUsage.forEach(usage => {
      const dayIndex = last7Days.findIndex(day => day.fullDate === usage.date);
      if (dayIndex !== -1) {
        last7Days[dayIndex].credits = usage.credits;
      }
    });

    return last7Days;
  }, [stats.dailyUsage]);

  const maxUsage = Math.max(...chartData.map(d => d.credits), 1);
  const totalWeeklyUsage = chartData.reduce((sum, day) => sum + day.credits, 0);
  const averageDailyUsage = totalWeeklyUsage / 7;

  // Calculate trend (comparing first half vs second half of the week)
  const firstHalf = chartData.slice(0, 3).reduce((sum, day) => sum + day.credits, 0) / 3;
  const secondHalf = chartData.slice(4).reduce((sum, day) => sum + day.credits, 0) / 3;
  const trend = secondHalf > firstHalf ? 'up' : secondHalf < firstHalf ? 'down' : 'stable';
  const trendPercentage = firstHalf > 0 ? Math.abs(((secondHalf - firstHalf) / firstHalf) * 100) : 0;

  return (
    <div className="bg-background-secondary rounded-lg p-6 border border-border-primary">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h3 className="text-lg font-semibold text-white">Usage Trends</h3>
          <p className="text-gray-400 text-sm">Last 7 days</p>
        </div>
        
        <div className="flex items-center space-x-2">
          {trend === 'up' ? (
            <HiTrendingUp className="w-5 h-5 text-green-400" />
          ) : trend === 'down' ? (
            <HiTrendingDown className="w-5 h-5 text-red-400" />
          ) : null}
          
          {trend !== 'stable' && (
            <span className={`text-sm font-medium ${
              trend === 'up' ? 'text-green-400' : 'text-red-400'
            }`}>
              {trendPercentage.toFixed(1)}%
            </span>
          )}
        </div>
      </div>

      {/* Chart */}
      <div className="mb-6">
        <div className="flex items-end justify-between h-32 space-x-2">
          {chartData.map((day, index) => {
            const height = maxUsage > 0 ? (day.credits / maxUsage) * 100 : 0;
            
            return (
              <div key={day.date} className="flex-1 flex flex-col items-center">
                <div className="w-full flex justify-center mb-2">
                  <motion.div
                    initial={{ height: 0 }}
                    animate={{ height: `${height}%` }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="w-full max-w-8 bg-gradient-to-t from-primary-500 to-primary-400 rounded-t-sm relative group"
                    style={{ minHeight: height > 0 ? '4px' : '0px' }}
                  >
                    {/* Tooltip */}
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <div className="bg-background-tertiary border border-border-primary rounded px-2 py-1 text-xs text-white whitespace-nowrap">
                        {day.credits} credits
                      </div>
                    </div>
                  </motion.div>
                </div>
                
                <span className="text-xs text-gray-400">{day.date}</span>
              </div>
            );
          })}
        </div>
      </div>

      {/* Stats Summary */}
      <div className="grid grid-cols-2 gap-4">
        <div className="bg-background-tertiary rounded-lg p-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-white">{totalWeeklyUsage}</div>
            <div className="text-gray-400 text-sm">Total This Week</div>
          </div>
        </div>
        
        <div className="bg-background-tertiary rounded-lg p-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-white">{averageDailyUsage.toFixed(1)}</div>
            <div className="text-gray-400 text-sm">Daily Average</div>
          </div>
        </div>
      </div>

      {/* Usage Efficiency */}
      <div className="mt-4 pt-4 border-t border-border-secondary">
        <h4 className="text-sm font-medium text-white mb-3">Usage Efficiency</h4>
        <div className="space-y-2">
          {Object.entries(stats.usageByOperation).slice(0, 3).map(([operation, credits]) => {
            const operationCost = operationCosts.find(cost => cost.operation_type === operation);
            const generations = operationCost ? Math.floor(credits / operationCost.credits_required) : 0;
            
            return (
              <div key={operation} className="flex justify-between items-center text-sm">
                <span className="text-gray-300 capitalize">
                  {operation.replace(/_/g, ' ')}
                </span>
                <span className="text-white">
                  {generations} generations
                </span>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
