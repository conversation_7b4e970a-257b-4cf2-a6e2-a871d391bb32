import{r as i,j as e,A as F,P as Q,B as o,z as M,m as O,J as W,G as Y,n as z,y as L,Q as T,S as X,C as B,f as J,T as ee,U,V as se,W as te,X as G,I as R,Y as V,b as ae,Z as re,_ as ne,$ as ie,a0 as le,w as ce,v as oe}from"./index-fc7af858.js";const q=[{id:"free",name:"Free",price:0,interval:"month",features:["10 AI generations per month","Basic flashcards and quizzes","Limited document uploads","Basic analytics"]},{id:"pro_monthly",name:"Pro",price:9.99,interval:"month",popular:!0,features:["Unlimited AI generations","Advanced study modes","Unlimited document uploads","Advanced analytics and insights","Priority support","Export capabilities"]},{id:"pro_yearly",name:"Pro (Annual)",price:99.99,interval:"year",features:["Unlimited AI generations","Advanced study modes","Unlimited document uploads","Advanced analytics and insights","Priority support","Export capabilities","2 months free!"]}],de=()=>{var g,A;const[a,C]=i.useState(null),[D,f]=i.useState(!0),[N,h]=i.useState(!1),[v,n]=i.useState(null);i.useEffect(()=>{p()},[]);const p=async()=>{f(!0),n(null);try{const s=localStorage.getItem("auth_token"),r=await fetch("/api/subscription",{headers:{Authorization:`Bearer ${s}`}});if(!r.ok)throw new Error("Failed to fetch subscription data");const c=await r.json();if(c.success)C(c.data);else throw new Error(c.error)}catch(s){n(s instanceof Error?s.message:"Failed to load subscription data"),C({currentPlan:q[0],status:"active",nextBillingDate:new Date(Date.now()+30*24*60*60*1e3).toISOString()})}finally{f(!1)}},d=async s=>{h(!0),n(null);try{const r=localStorage.getItem("auth_token"),c=await fetch("/api/subscription/change",{method:"POST",headers:{Authorization:`Bearer ${r}`,"Content-Type":"application/json"},body:JSON.stringify({planId:s})});if(!c.ok)throw new Error("Failed to change subscription plan");const S=await c.json();if(S.success)await p();else throw new Error(S.error)}catch(r){n(r instanceof Error?r.message:"Failed to change subscription plan")}finally{h(!1)}},y=async()=>{if(confirm("Are you sure you want to cancel your subscription? You will lose access to Pro features at the end of your billing period.")){h(!0),n(null);try{const s=localStorage.getItem("auth_token"),r=await fetch("/api/subscription/cancel",{method:"POST",headers:{Authorization:`Bearer ${s}`}});if(!r.ok)throw new Error("Failed to cancel subscription");const c=await r.json();if(c.success)await p();else throw new Error(c.error)}catch(s){n(s instanceof Error?s.message:"Failed to cancel subscription")}finally{h(!1)}}},l=async()=>{h(!0),n(null);try{const s=localStorage.getItem("auth_token"),r=await fetch("/api/subscription/reactivate",{method:"POST",headers:{Authorization:`Bearer ${s}`}});if(!r.ok)throw new Error("Failed to reactivate subscription");const c=await r.json();if(c.success)await p();else throw new Error(c.error)}catch(s){n(s instanceof Error?s.message:"Failed to reactivate subscription")}finally{h(!1)}};return D?e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"animate-pulse",children:[e.jsx("div",{className:"h-8 w-48 bg-gray-600 rounded mb-4"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[1,2,3].map(s=>e.jsx("div",{className:"h-64 bg-gray-600 rounded-lg"},s))})]})}):e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Subscription Management"}),v&&e.jsxs("div",{className:"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(F,{className:"w-5 h-5 text-red-400"}),e.jsx("span",{className:"text-red-400 font-medium",children:"Error"})]}),e.jsx("p",{className:"text-red-300 mt-1",children:v})]}),a&&e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-medium text-white",children:"Current Plan"}),e.jsx("p",{className:"text-gray-400",children:((g=a.currentPlan)==null?void 0:g.name)||"No active plan"})]}),e.jsx("div",{className:"text-right",children:e.jsx("div",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${a.status==="active"?"bg-green-500/20 text-green-400":a.status==="canceled"?"bg-red-500/20 text-red-400":a.status==="past_due"?"bg-yellow-500/20 text-yellow-400":"bg-blue-500/20 text-blue-400"}`,children:a.status.charAt(0).toUpperCase()+a.status.slice(1)})})]}),a.nextBillingDate&&e.jsxs("div",{className:"flex items-center space-x-2 text-gray-400 text-sm",children:[e.jsx(Q,{className:"w-4 h-4"}),e.jsx("span",{children:a.cancelAtPeriodEnd?`Access ends on ${new Date(a.nextBillingDate).toLocaleDateString()}`:`Next billing date: ${new Date(a.nextBillingDate).toLocaleDateString()}`})]}),((A=a.currentPlan)==null?void 0:A.id)!=="free"&&e.jsxs("div",{className:"mt-4 flex space-x-3",children:[a.cancelAtPeriodEnd?e.jsx(o,{onClick:l,isLoading:N,variant:"primary",size:"sm",children:"Reactivate Subscription"}):e.jsx(o,{onClick:y,isLoading:N,variant:"danger",size:"sm",children:"Cancel Subscription"}),e.jsxs(o,{onClick:p,variant:"secondary",size:"sm",children:[e.jsx(M,{className:"w-4 h-4 mr-2"}),"Refresh"]})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-medium text-white mb-4",children:"Available Plans"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:q.map(s=>{var c;const r=((c=a==null?void 0:a.currentPlan)==null?void 0:c.id)===s.id;return e.jsxs(O.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},className:`relative bg-background-secondary rounded-lg p-6 border transition-all duration-200 ${s.popular?"border-primary-500 ring-2 ring-primary-500/20":r?"border-green-500 ring-2 ring-green-500/20":"border-border-primary hover:border-gray-500"}`,children:[s.popular&&e.jsx("div",{className:"absolute -top-3 left-1/2 transform -translate-x-1/2",children:e.jsxs("div",{className:"bg-primary-500 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center space-x-1",children:[e.jsx(W,{className:"w-3 h-3"}),e.jsx("span",{children:"Most Popular"})]})}),r&&e.jsx("div",{className:"absolute -top-3 right-4",children:e.jsxs("div",{className:"bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center space-x-1",children:[e.jsx(Y,{className:"w-3 h-3"}),e.jsx("span",{children:"Current"})]})}),e.jsxs("div",{className:"text-center mb-6",children:[e.jsx("h5",{className:"text-xl font-semibold text-white mb-2",children:s.name}),e.jsxs("div",{className:"text-3xl font-bold text-white",children:["$",s.price,e.jsxs("span",{className:"text-lg text-gray-400",children:["/",s.interval]})]})]}),e.jsx("ul",{className:"space-y-3 mb-6",children:s.features.map((S,E)=>e.jsxs("li",{className:"flex items-center space-x-2",children:[e.jsx(Y,{className:"w-4 h-4 text-green-400 flex-shrink-0"}),e.jsx("span",{className:"text-gray-300 text-sm",children:S})]},E))}),e.jsx(o,{onClick:()=>d(s.id),disabled:r||N,isLoading:N,variant:s.popular?"primary":"secondary",className:"w-full",children:r?"Current Plan":`Switch to ${s.name}`})]},s.id)})})]})]})})},me=()=>{const[a,C]=i.useState(!1),[D,f]=i.useState(!1),[N,h]=i.useState(!1),[v,n]=i.useState(null),[p,d]=i.useState(null),[y,l]=i.useState({studySets:12,flashcards:245,quizzes:18,documents:8,totalSize:"2.4 MB"}),[g,A]=i.useState({studySets:!0,flashcards:!0,quizzes:!0,analytics:!0,preferences:!0}),s=i.useRef(null),r=async()=>{C(!0),n(null),d(null);try{const m=localStorage.getItem("auth_token"),x=await fetch("/api/data/export",{method:"POST",headers:{Authorization:`Bearer ${m}`,"Content-Type":"application/json"},body:JSON.stringify({exportData:g})});if(!x.ok)throw new Error("Failed to export data");const k=await x.blob(),u=window.URL.createObjectURL(k),j=document.createElement("a");j.href=u,j.download=`chewyai-data-export-${new Date().toISOString().split("T")[0]}.json`,j.click(),window.URL.revokeObjectURL(u),d("Data exported successfully!")}catch(m){n(m instanceof Error?m.message:"Failed to export data")}finally{C(!1)}},c=async m=>{f(!0),n(null),d(null);try{const x=new FormData;x.append("file",m);const k=localStorage.getItem("auth_token"),u=await fetch("/api/data/import",{method:"POST",headers:{Authorization:`Bearer ${k}`},body:x});if(!u.ok)throw new Error("Failed to import data");const j=await u.json();if(j.success)d(`Data imported successfully! ${j.imported} items processed.`),await P();else throw new Error(j.error)}catch(x){n(x instanceof Error?x.message:"Failed to import data")}finally{f(!1)}},S=m=>{var k;const x=(k=m.target.files)==null?void 0:k[0];if(x){if(x.type!=="application/json"){n("Please select a valid JSON file");return}c(x)}},E=async m=>{if(confirm(`Are you sure you want to clear all ${m}? This action cannot be undone.`)){h(!0),n(null),d(null);try{const x=localStorage.getItem("auth_token"),k=await fetch(`/api/data/clear/${m}`,{method:"DELETE",headers:{Authorization:`Bearer ${x}`}});if(!k.ok)throw new Error(`Failed to clear ${m}`);const u=await k.json();if(u.success)d(`${m} cleared successfully!`),await P();else throw new Error(u.error)}catch(x){n(x instanceof Error?x.message:`Failed to clear ${m}`)}finally{h(!1)}}},P=async()=>{try{const m=localStorage.getItem("auth_token"),x=await fetch("/api/data/stats",{headers:{Authorization:`Bearer ${m}`}});if(x.ok){const k=await x.json();k.success&&l(k.data)}}catch{}};return e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Data Management"}),v&&e.jsxs("div",{className:"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(F,{className:"w-5 h-5 text-red-400"}),e.jsx("span",{className:"text-red-400 font-medium",children:"Error"})]}),e.jsx("p",{className:"text-red-300 mt-1",children:v})]}),p&&e.jsxs("div",{className:"mb-6 bg-green-500/20 border border-green-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(z,{className:"w-5 h-5 text-green-400"}),e.jsx("span",{className:"text-green-400 font-medium",children:"Success"})]}),e.jsx("p",{className:"text-green-300 mt-1",children:p})]}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h4",{className:"text-lg font-medium text-white",children:"Your Data Overview"}),e.jsxs(o,{onClick:P,variant:"secondary",size:"sm",children:[e.jsx(M,{className:"w-4 h-4 mr-2"}),"Refresh"]})]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary-400",children:y.studySets}),e.jsx("div",{className:"text-sm text-gray-400",children:"Study Sets"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary-400",children:y.flashcards}),e.jsx("div",{className:"text-sm text-gray-400",children:"Flashcards"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary-400",children:y.quizzes}),e.jsx("div",{className:"text-sm text-gray-400",children:"Quizzes"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary-400",children:y.documents}),e.jsx("div",{className:"text-sm text-gray-400",children:"Documents"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary-400",children:y.totalSize}),e.jsx("div",{className:"text-sm text-gray-400",children:"Total Size"})]})]})]}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[e.jsx(L,{className:"w-6 h-6 text-blue-400"}),e.jsx("h4",{className:"text-lg font-medium text-white",children:"Export Your Data"})]}),e.jsx("p",{className:"text-gray-400 text-sm mb-4",children:"Download a copy of your data in JSON format. You can use this to backup your data or import it into another account."}),e.jsxs("div",{className:"space-y-3 mb-6",children:[e.jsx("h5",{className:"font-medium text-white",children:"Select data to export:"}),Object.entries(g).map(([m,x])=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm text-gray-300 capitalize",children:m.replace(/([A-Z])/g," $1").trim()}),e.jsx("button",{onClick:()=>A({...g,[m]:!x}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${x?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${x?"translate-x-6":"translate-x-1"}`})})]},m))]}),e.jsxs(o,{onClick:r,isLoading:a,variant:"primary",children:[e.jsx(L,{className:"w-4 h-4 mr-2"}),"Export Data"]})]}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[e.jsx(T,{className:"w-6 h-6 text-green-400"}),e.jsx("h4",{className:"text-lg font-medium text-white",children:"Import Data"})]}),e.jsx("p",{className:"text-gray-400 text-sm mb-4",children:"Import data from a previously exported JSON file. This will merge with your existing data."}),e.jsxs("div",{className:"bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4 mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(Q,{className:"w-5 h-5 text-yellow-400"}),e.jsx("span",{className:"text-yellow-400 font-medium",children:"Important"})]}),e.jsx("p",{className:"text-yellow-300 text-sm mt-1",children:"Importing data will merge with your existing data. Duplicate items may be created."})]}),e.jsx("input",{ref:s,type:"file",accept:".json",onChange:S,className:"hidden"}),e.jsxs(o,{onClick:()=>{var m;return(m=s.current)==null?void 0:m.click()},isLoading:D,variant:"secondary",children:[e.jsx(T,{className:"w-4 h-4 mr-2"}),"Select File to Import"]})]}),e.jsxs("div",{className:"bg-red-500/10 rounded-lg p-6 border border-red-500/30",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[e.jsx(X,{className:"w-6 h-6 text-red-400"}),e.jsx("h4",{className:"text-lg font-medium text-white",children:"Clear Data"})]}),e.jsx("p",{className:"text-gray-400 text-sm mb-6",children:"Permanently delete specific types of data from your account. This action cannot be undone."}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsx(o,{onClick:()=>E("study-sets"),isLoading:N,variant:"danger",size:"sm",className:"w-full",children:"Clear All Study Sets"}),e.jsx(o,{onClick:()=>E("flashcards"),isLoading:N,variant:"danger",size:"sm",className:"w-full",children:"Clear All Flashcards"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx(o,{onClick:()=>E("quizzes"),isLoading:N,variant:"danger",size:"sm",className:"w-full",children:"Clear All Quizzes"}),e.jsx(o,{onClick:()=>E("analytics"),isLoading:N,variant:"danger",size:"sm",className:"w-full",children:"Clear Analytics Data"})]})]})]})]})})},xe=()=>{const[a,C]=i.useState(null),[D,f]=i.useState(!0),[N,h]=i.useState(!1),[v,n]=i.useState(null);i.useEffect(()=>{p()},[]);const p=async()=>{f(!0),n(null);try{const s=localStorage.getItem("auth_token"),r=await fetch("/api/billing",{headers:{Authorization:`Bearer ${s}`}});if(!r.ok)throw new Error("Failed to fetch billing data");const c=await r.json();if(c.success)C(c.data);else throw new Error(c.error)}catch(s){n(s instanceof Error?s.message:"Failed to load billing data"),C({paymentMethods:[{id:"1",type:"card",last4:"4242",brand:"visa",expiryMonth:12,expiryYear:2025,isDefault:!0}],invoices:[{id:"1",number:"INV-001",amount:9.99,currency:"USD",status:"paid",date:new Date(Date.now()-30*24*60*60*1e3).toISOString(),description:"Pro Plan - Monthly"},{id:"2",number:"INV-002",amount:9.99,currency:"USD",status:"pending",date:new Date().toISOString(),dueDate:new Date(Date.now()+7*24*60*60*1e3).toISOString(),description:"Pro Plan - Monthly"}],nextInvoice:{amount:9.99,currency:"USD",date:new Date(Date.now()+30*24*60*60*1e3).toISOString()}})}finally{f(!1)}},d=async s=>{try{const r=localStorage.getItem("auth_token"),c=await fetch(`/api/billing/invoices/${s}/download`,{headers:{Authorization:`Bearer ${r}`}});if(!c.ok)throw new Error("Failed to download invoice");const S=await c.blob(),E=window.URL.createObjectURL(S),P=document.createElement("a");P.href=E,P.download=`invoice-${s}.pdf`,P.click(),window.URL.revokeObjectURL(E)}catch(r){n(r instanceof Error?r.message:"Failed to download invoice")}},y=async()=>{n("Payment method management coming soon")},l=async s=>{h(!0),n(null);try{const r=localStorage.getItem("auth_token"),c=await fetch("/api/billing/payment-methods/default",{method:"POST",headers:{Authorization:`Bearer ${r}`,"Content-Type":"application/json"},body:JSON.stringify({paymentMethodId:s})});if(!c.ok)throw new Error("Failed to update default payment method");const S=await c.json();if(S.success)await p();else throw new Error(S.error)}catch(r){n(r instanceof Error?r.message:"Failed to update payment method")}finally{h(!1)}},g=s=>{switch(s){case"paid":return e.jsx(z,{className:"w-5 h-5 text-green-400"});case"pending":return e.jsx(J,{className:"w-5 h-5 text-yellow-400"});case"failed":return e.jsx(ee,{className:"w-5 h-5 text-red-400"});default:return e.jsx(J,{className:"w-5 h-5 text-gray-400"})}},A=s=>{switch(s){case"paid":return"text-green-400";case"pending":return"text-yellow-400";case"failed":return"text-red-400";default:return"text-gray-400"}};return D?e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"animate-pulse",children:[e.jsx("div",{className:"h-8 w-48 bg-gray-600 rounded mb-4"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"h-32 bg-gray-600 rounded-lg"}),e.jsx("div",{className:"h-64 bg-gray-600 rounded-lg"})]})]})}):e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Billing & Invoices"}),e.jsxs(o,{onClick:p,variant:"secondary",size:"sm",children:[e.jsx(M,{className:"w-4 h-4 mr-2"}),"Refresh"]})]}),v&&e.jsxs("div",{className:"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(F,{className:"w-5 h-5 text-red-400"}),e.jsx("span",{className:"text-red-400 font-medium",children:"Error"})]}),e.jsx("p",{className:"text-red-300 mt-1",children:v})]}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h4",{className:"text-lg font-medium text-white",children:"Payment Methods"}),e.jsx(o,{onClick:y,variant:"secondary",size:"sm",children:"Add Payment Method"})]}),(a==null?void 0:a.paymentMethods.length)===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(B,{className:"w-16 h-16 text-gray-500 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-400",children:"No payment methods added"})]}):e.jsx("div",{className:"space-y-3",children:a==null?void 0:a.paymentMethods.map(s=>{var r;return e.jsxs("div",{className:"flex items-center justify-between p-4 bg-background-secondary rounded-lg border border-border-primary",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(B,{className:"w-6 h-6 text-gray-400"}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("span",{className:"text-white font-medium",children:[(r=s.brand)==null?void 0:r.toUpperCase()," •••• ",s.last4]}),s.isDefault&&e.jsx("span",{className:"bg-primary-500/20 text-primary-400 px-2 py-1 rounded text-xs font-medium",children:"Default"})]}),s.expiryMonth&&s.expiryYear&&e.jsxs("p",{className:"text-gray-400 text-sm",children:["Expires ",s.expiryMonth.toString().padStart(2,"0"),"/",s.expiryYear]})]})]}),!s.isDefault&&e.jsx(o,{onClick:()=>l(s.id),isLoading:N,variant:"secondary",size:"sm",children:"Set as Default"})]},s.id)})})]}),(a==null?void 0:a.nextInvoice)&&e.jsxs("div",{className:"bg-blue-500/10 rounded-lg p-6 border border-blue-500/30 mb-6",children:[e.jsx("h4",{className:"text-lg font-medium text-white mb-2",children:"Upcoming Invoice"}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsxs("p",{className:"text-blue-300",children:["$",a.nextInvoice.amount," ",a.nextInvoice.currency.toUpperCase()]}),e.jsxs("p",{className:"text-blue-400 text-sm",children:["Due on ",new Date(a.nextInvoice.date).toLocaleDateString()]})]})})]}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary",children:[e.jsx("h4",{className:"text-lg font-medium text-white mb-4",children:"Invoice History"}),(a==null?void 0:a.invoices.length)===0?e.jsx("div",{className:"text-center py-8",children:e.jsx("p",{className:"text-gray-400",children:"No invoices found"})}):e.jsx("div",{className:"space-y-3",children:a==null?void 0:a.invoices.map(s=>e.jsxs(O.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"flex items-center justify-between p-4 bg-background-secondary rounded-lg border border-border-primary",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[g(s.status),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-white font-medium",children:s.number}),e.jsx("span",{className:`text-sm font-medium ${A(s.status)}`,children:s.status.charAt(0).toUpperCase()+s.status.slice(1)})]}),e.jsx("p",{className:"text-gray-400 text-sm",children:s.description}),e.jsxs("p",{className:"text-gray-500 text-xs",children:[new Date(s.date).toLocaleDateString(),s.dueDate&&s.status==="pending"&&e.jsxs("span",{children:[" • Due ",new Date(s.dueDate).toLocaleDateString()]})]})]})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("span",{className:"text-white font-medium",children:["$",s.amount," ",s.currency.toUpperCase()]}),s.status==="paid"&&e.jsxs(o,{onClick:()=>d(s.id),variant:"secondary",size:"sm",children:[e.jsx(L,{className:"w-4 h-4 mr-2"}),"Download"]})]})]},s.id))})]})]})})},ue=({enabled:a,onToggle:C})=>{const[D,f]=i.useState(!1),[N,h]=i.useState(!1),[v,n]=i.useState(null),[p,d]=i.useState(null),[y,l]=i.useState(null),[g,A]=i.useState(""),[s,r]=i.useState(!1),[c,S]=i.useState(!1),E=async()=>{h(!0),n(null),d(null);try{const u=localStorage.getItem("auth_token"),j=await fetch("/api/auth/2fa/setup",{method:"POST",headers:{Authorization:`Bearer ${u}`}});if(!j.ok)throw new Error("Failed to setup 2FA");const I=await j.json();if(I.success)l(I.data),f(!0);else throw new Error(I.error)}catch(u){n(u instanceof Error?u.message:"Failed to setup 2FA"),l({qrCode:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",secret:"JBSWY3DPEHPK3PXP",backupCodes:["12345678","87654321","11111111","22222222","33333333","44444444","55555555","66666666"]}),f(!0)}finally{h(!1)}},P=async()=>{if(!g||g.length!==6){n("Please enter a valid 6-digit code");return}h(!0),n(null);try{const u=localStorage.getItem("auth_token"),j=await fetch("/api/auth/2fa/verify",{method:"POST",headers:{Authorization:`Bearer ${u}`,"Content-Type":"application/json"},body:JSON.stringify({code:g})});if(!j.ok)throw new Error("Failed to verify 2FA code");const I=await j.json();if(I.success)d("Two-factor authentication enabled successfully!"),f(!1),S(!0),C(!0);else throw new Error(I.error||"Invalid verification code")}catch(u){n(u instanceof Error?u.message:"Failed to verify 2FA code")}finally{h(!1)}},m=async()=>{if(confirm("Are you sure you want to disable two-factor authentication? This will make your account less secure.")){h(!0),n(null);try{const u=localStorage.getItem("auth_token"),j=await fetch("/api/auth/2fa/disable",{method:"POST",headers:{Authorization:`Bearer ${u}`}});if(!j.ok)throw new Error("Failed to disable 2FA");const I=await j.json();if(I.success)d("Two-factor authentication disabled successfully"),C(!1);else throw new Error(I.error)}catch(u){n(u instanceof Error?u.message:"Failed to disable 2FA")}finally{h(!1)}}},x=u=>{navigator.clipboard.writeText(u),d("Copied to clipboard!"),setTimeout(()=>d(null),2e3)},k=()=>{f(!1),l(null),A(""),n(null),d(null)};return D&&y?e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-6",children:[e.jsx(U,{className:"w-6 h-6 text-primary-400"}),e.jsx("h4",{className:"text-lg font-medium text-white",children:"Setup Two-Factor Authentication"})]}),v&&e.jsx("div",{className:"mb-4 bg-red-500/20 border border-red-500/30 rounded-lg p-3",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(F,{className:"w-4 h-4 text-red-400"}),e.jsx("span",{className:"text-red-400 text-sm",children:v})]})}),p&&e.jsx("div",{className:"mb-4 bg-green-500/20 border border-green-500/30 rounded-lg p-3",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(z,{className:"w-4 h-4 text-green-400"}),e.jsx("span",{className:"text-green-400 text-sm",children:p})]})}),c?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx(z,{className:"w-16 h-16 text-green-400 mx-auto mb-4"}),e.jsx("h5",{className:"text-lg font-medium text-white mb-2",children:"2FA Enabled Successfully!"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Your account is now protected with two-factor authentication."})]}),e.jsxs("div",{className:"bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4",children:[e.jsx("h5",{className:"font-medium text-yellow-400 mb-2",children:"Important: Save Your Backup Codes"}),e.jsx("p",{className:"text-yellow-300 text-sm mb-4",children:"Store these backup codes in a safe place. You can use them to access your account if you lose your authenticator device."}),e.jsx("div",{className:"grid grid-cols-2 gap-2 mb-4",children:y.backupCodes.map((u,j)=>e.jsx("div",{className:"bg-background-secondary border border-border-primary rounded p-2 text-center",children:e.jsx("code",{className:"text-primary-400 font-mono",children:u})},j))}),e.jsxs(o,{onClick:()=>x(y.backupCodes.join(`
`)),variant:"secondary",size:"sm",children:[e.jsx(G,{className:"w-4 h-4 mr-2"}),"Copy All Codes"]})]}),e.jsx(o,{onClick:()=>S(!1),variant:"primary",className:"w-full",children:"I've Saved My Backup Codes"})]}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h5",{className:"font-medium text-white mb-2",children:"Step 1: Scan QR Code"}),e.jsx("p",{className:"text-gray-400 text-sm mb-4",children:"Scan this QR code with your authenticator app (Google Authenticator, Authy, etc.)"}),e.jsx("div",{className:"bg-white p-4 rounded-lg inline-block",children:e.jsx("img",{src:y.qrCode,alt:"2FA QR Code",className:"w-48 h-48"})})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-white mb-2",children:"Step 2: Manual Entry (Alternative)"}),e.jsx("p",{className:"text-gray-400 text-sm mb-3",children:"If you can't scan the QR code, enter this secret key manually:"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"flex-1 bg-background-secondary border border-border-primary rounded-lg p-3",children:e.jsx("code",{className:"text-primary-400 font-mono",children:s?y.secret:"••••••••••••••••"})}),e.jsx(o,{onClick:()=>r(!s),variant:"secondary",size:"sm",children:s?e.jsx(se,{className:"w-4 h-4"}):e.jsx(te,{className:"w-4 h-4"})}),e.jsx(o,{onClick:()=>x(y.secret),variant:"secondary",size:"sm",children:e.jsx(G,{className:"w-4 h-4"})})]})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-white mb-2",children:"Step 3: Verify Setup"}),e.jsx("p",{className:"text-gray-400 text-sm mb-3",children:"Enter the 6-digit code from your authenticator app:"}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx(R,{value:g,onChange:A,placeholder:"123456",className:"flex-1"}),e.jsx(o,{onClick:P,isLoading:N,disabled:g.length!==6,children:"Verify"})]})]}),e.jsx("div",{className:"flex space-x-3",children:e.jsx(o,{onClick:k,variant:"secondary",className:"flex-1",children:"Cancel"})})]})]}):e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(U,{className:`w-6 h-6 ${a?"text-green-400":"text-gray-400"}`}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-medium text-white",children:"Two-Factor Authentication"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Add an extra layer of security to your account"})]})]}),e.jsx("div",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${a?"bg-green-500/20 text-green-400":"bg-gray-500/20 text-gray-400"}`,children:a?"Enabled":"Disabled"})]}),v&&e.jsx("div",{className:"mb-4 bg-red-500/20 border border-red-500/30 rounded-lg p-3",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(F,{className:"w-4 h-4 text-red-400"}),e.jsx("span",{className:"text-red-400 text-sm",children:v})]})}),p&&e.jsx("div",{className:"mb-4 bg-green-500/20 border border-green-500/30 rounded-lg p-3",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(z,{className:"w-4 h-4 text-green-400"}),e.jsx("span",{className:"text-green-400 text-sm",children:p})]})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-gray-300 text-sm",children:a?"Two-factor authentication is currently enabled for your account. You can disable it below if needed.":"Enable two-factor authentication to add an extra layer of security to your account. You'll need an authenticator app like Google Authenticator or Authy."}),e.jsx("div",{className:"flex space-x-3",children:a?e.jsx(o,{onClick:m,isLoading:N,variant:"danger",children:"Disable 2FA"}):e.jsxs(o,{onClick:E,isLoading:N,variant:"primary",children:[e.jsx(V,{className:"w-4 h-4 mr-2"}),"Enable 2FA"]})})]})]})},he=[{id:"profile",label:"Profile",icon:re,description:"Manage your account information"},{id:"preferences",label:"Preferences",icon:ne,description:"Customize your experience"},{id:"notifications",label:"Notifications",icon:ie,description:"Control notification settings"},{id:"security",label:"Security",icon:U,description:"Password and security settings"},{id:"subscription",label:"Subscription",icon:B,description:"Manage your subscription plan"},{id:"billing",label:"Billing",icon:B,description:"Payment history and invoices"},{id:"data",label:"Data Management",icon:le,description:"Export, import, and manage your data"}],ge=()=>{const[a,C]=i.useState("profile"),[D,f]=i.useState(!1),[N,h]=i.useState(null),[v,n]=i.useState(null),{user:p}=ae(),[d,y]=i.useState({name:(p==null?void 0:p.name)||"",email:(p==null?void 0:p.email)||"",bio:"",avatar:null}),[l,g]=i.useState({theme:"dark",language:"en",studyReminders:!0,autoSave:!0,defaultStudyMode:"flashcards",sessionDuration:30,difficultyLevel:"medium"}),[A,s]=i.useState({emailNotifications:!0,studyReminders:!0,weeklyProgress:!1,marketingEmails:!1,achievementNotifications:!0,streakReminders:!0}),[r,c]=i.useState({twoFactorEnabled:!1,loginNotifications:!0,sessionTimeout:30});i.useEffect(()=>{(async()=>{try{f(!0)}catch{h("Failed to load user settings")}finally{f(!1)}})()},[]);const S=async()=>{f(!0),h(null),n(null);try{const t=new FormData;t.append("name",d.name),t.append("bio",d.bio),d.avatar&&t.append("avatar",d.avatar);const w=localStorage.getItem("auth_token"),b=await fetch("/api/user/profile",{method:"PUT",headers:{Authorization:`Bearer ${w}`},body:t});if(!b.ok)throw new Error("Failed to update profile");const $=await b.json();if($.success)n("Profile updated successfully!");else throw new Error($.error)}catch(t){h(t instanceof Error?t.message:"Failed to update profile")}finally{f(!1)}},E=async()=>{f(!0),h(null),n(null);try{const t=localStorage.getItem("auth_token"),w=await fetch("/api/user/preferences",{method:"PUT",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify(l)});if(!w.ok)throw new Error("Failed to update preferences");const b=await w.json();if(b.success)n("Preferences updated successfully!");else throw new Error(b.error)}catch(t){h(t instanceof Error?t.message:"Failed to update preferences")}finally{f(!1)}},P=async()=>{f(!0),h(null),n(null);try{const t=localStorage.getItem("auth_token"),w=await fetch("/api/user/notifications",{method:"PUT",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify(A)});if(!w.ok)throw new Error("Failed to update notification settings");const b=await w.json();if(b.success)n("Notification settings updated successfully!");else throw new Error(b.error)}catch(t){h(t instanceof Error?t.message:"Failed to update notification settings")}finally{f(!1)}},m=()=>{var t,w;return e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Profile Information"}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Profile Picture"}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white text-2xl font-bold",children:((w=(t=p==null?void 0:p.name)==null?void 0:t.charAt(0))==null?void 0:w.toUpperCase())||"U"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(o,{variant:"secondary",size:"sm",onClick:()=>{const b=document.createElement("input");b.type="file",b.accept="image/*",b.onchange=$=>{var _;const H=(_=$.target.files)==null?void 0:_[0];H&&y({...d,avatar:H})},b.click()},children:[e.jsx(T,{className:"w-4 h-4 mr-2"}),"Upload Photo"]}),e.jsx("p",{className:"text-xs text-gray-500",children:"JPG, PNG up to 5MB"})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(R,{label:"Full Name",value:d.name,onChange:b=>y({...d,name:b}),placeholder:"Enter your full name"}),e.jsx(R,{label:"Email Address",type:"email",value:d.email,onChange:b=>y({...d,email:b}),placeholder:"Enter your email",disabled:!0}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Bio (Optional)"}),e.jsx("textarea",{value:d.bio,onChange:b=>y({...d,bio:b.target.value}),placeholder:"Tell us about yourself...",rows:4,className:"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]})]}),e.jsx("div",{className:"mt-6",children:e.jsx(o,{onClick:S,isLoading:D,children:"Save Profile"})})]})})},x=()=>e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"App Preferences"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Theme"}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsxs("button",{onClick:()=>g({...l,theme:"dark"}),className:`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${l.theme==="dark"?"border-primary-500 bg-primary-500/20 text-primary-400":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:[e.jsx(ce,{className:"w-4 h-4"}),e.jsx("span",{children:"Dark"})]}),e.jsxs("button",{onClick:()=>g({...l,theme:"light"}),className:`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${l.theme==="light"?"border-primary-500 bg-primary-500/20 text-primary-400":"border-gray-600 text-gray-300 hover:border-gray-500"}`,disabled:!0,children:[e.jsx(oe,{className:"w-4 h-4"}),e.jsx("span",{children:"Light (Coming Soon)"})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Language"}),e.jsxs("select",{value:l.language,onChange:t=>g({...l,language:t.target.value}),className:"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:[e.jsx("option",{value:"en",children:"English"}),e.jsx("option",{value:"es",disabled:!0,children:"Spanish (Coming Soon)"}),e.jsx("option",{value:"fr",disabled:!0,children:"French (Coming Soon)"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Default Study Mode"}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx("button",{onClick:()=>g({...l,defaultStudyMode:"flashcards"}),className:`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${l.defaultStudyMode==="flashcards"?"border-primary-500 bg-primary-500/20 text-primary-400":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:e.jsx("span",{children:"Flashcards"})}),e.jsx("button",{onClick:()=>g({...l,defaultStudyMode:"quiz"}),className:`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${l.defaultStudyMode==="quiz"?"border-primary-500 bg-primary-500/20 text-primary-400":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:e.jsx("span",{children:"Quiz"})})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Default Session Duration (minutes)"}),e.jsxs("select",{value:l.sessionDuration,onChange:t=>g({...l,sessionDuration:parseInt(t.target.value)}),className:"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:[e.jsx("option",{value:15,children:"15 minutes"}),e.jsx("option",{value:30,children:"30 minutes"}),e.jsx("option",{value:45,children:"45 minutes"}),e.jsx("option",{value:60,children:"1 hour"}),e.jsx("option",{value:90,children:"1.5 hours"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Default Difficulty Level"}),e.jsx("div",{className:"flex space-x-4",children:["easy","medium","hard"].map(t=>e.jsx("button",{onClick:()=>g({...l,difficultyLevel:t}),className:`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${l.difficultyLevel===t?"border-primary-500 bg-primary-500/20 text-primary-400":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:e.jsx("span",{className:"capitalize",children:t})},t))})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-300",children:"Study Reminders"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Get reminded to study regularly"})]}),e.jsx("button",{onClick:()=>g({...l,studyReminders:!l.studyReminders}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${l.studyReminders?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${l.studyReminders?"translate-x-6":"translate-x-1"}`})})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-300",children:"Auto-save"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Automatically save your progress"})]}),e.jsx("button",{onClick:()=>g({...l,autoSave:!l.autoSave}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${l.autoSave?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${l.autoSave?"translate-x-6":"translate-x-1"}`})})]})]})]}),e.jsx("div",{className:"mt-6",children:e.jsx(o,{onClick:E,isLoading:D,children:"Save Preferences"})})]})}),k=()=>e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Notification Settings"}),e.jsx("div",{className:"space-y-4",children:Object.entries(A).map(([t,w])=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-300 capitalize",children:t.replace(/([A-Z])/g," $1").trim()}),e.jsxs("p",{className:"text-xs text-gray-500",children:[t==="emailNotifications"&&"Receive important updates via email",t==="studyReminders"&&"Get reminded when it's time to study",t==="weeklyProgress"&&"Weekly summary of your study progress",t==="marketingEmails"&&"Product updates and tips",t==="achievementNotifications"&&"Get notified when you unlock achievements",t==="streakReminders"&&"Reminders to maintain your study streak"]})]}),e.jsx("button",{onClick:()=>s({...A,[t]:!w}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${w?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${w?"translate-x-6":"translate-x-1"}`})})]},t))}),e.jsx("div",{className:"mt-6",children:e.jsx(o,{onClick:P,isLoading:D,children:"Save Notification Settings"})})]})}),u=()=>e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Security Settings"}),e.jsxs("div",{className:"space-y-6",children:[e.jsx(ue,{enabled:r.twoFactorEnabled,onToggle:t=>c({...r,twoFactorEnabled:t})}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-4 border border-border-primary",children:[e.jsx("h4",{className:"font-medium text-white mb-4",children:"Session Management"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-300",children:"Login Notifications"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Get notified when someone logs into your account"})]}),e.jsx("button",{onClick:()=>c({...r,loginNotifications:!r.loginNotifications}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${r.loginNotifications?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${r.loginNotifications?"translate-x-6":"translate-x-1"}`})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Session Timeout (minutes)"}),e.jsxs("select",{value:r.sessionTimeout,onChange:t=>c({...r,sessionTimeout:parseInt(t.target.value)}),className:"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:[e.jsx("option",{value:15,children:"15 minutes"}),e.jsx("option",{value:30,children:"30 minutes"}),e.jsx("option",{value:60,children:"1 hour"}),e.jsx("option",{value:120,children:"2 hours"}),e.jsx("option",{value:0,children:"Never"})]})]})]})]}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-4 border border-border-primary",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-3",children:[e.jsx(V,{className:"w-5 h-5 text-primary-400"}),e.jsx("h4",{className:"font-medium text-white",children:"Change Password"})]}),e.jsx("p",{className:"text-gray-400 text-sm mb-4",children:"Update your password to keep your account secure"}),e.jsx(o,{variant:"secondary",children:"Change Password"})]}),e.jsxs("div",{className:"bg-red-500/10 rounded-lg p-4 border border-red-500/30",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-3",children:[e.jsx(F,{className:"w-5 h-5 text-red-400"}),e.jsx("h4",{className:"font-medium text-white",children:"Danger Zone"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-red-400 mb-2",children:"Account Deactivation"}),e.jsx("p",{className:"text-gray-400 text-sm mb-3",children:"Temporarily deactivate your account. You can reactivate it later."}),e.jsx(o,{variant:"secondary",size:"sm",children:"Deactivate Account"})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-red-400 mb-2",children:"Account Deletion"}),e.jsx("p",{className:"text-gray-400 text-sm mb-3",children:"Permanently delete your account and all associated data. This action cannot be undone."}),e.jsx(o,{variant:"danger",size:"sm",children:"Delete Account"})]})]})]})]})]})}),j=()=>e.jsx(de,{}),I=()=>e.jsx(xe,{}),Z=()=>e.jsx(me,{}),K=()=>{switch(a){case"profile":return m();case"preferences":return x();case"notifications":return k();case"security":return u();case"subscription":return j();case"billing":return I();case"data":return Z();default:return m()}};return e.jsx("div",{className:"min-h-screen bg-background-primary text-white",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Settings"}),e.jsx("p",{className:"text-gray-400",children:"Manage your account and preferences"})]}),N&&e.jsxs("div",{className:"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(F,{className:"w-5 h-5 text-red-400"}),e.jsx("span",{className:"text-red-400 font-medium",children:"Error"})]}),e.jsx("p",{className:"text-red-300 mt-1",children:N}),e.jsx(o,{onClick:()=>h(null),variant:"secondary",size:"sm",className:"mt-2",children:"Dismiss"})]}),v&&e.jsxs("div",{className:"mb-6 bg-green-500/20 border border-green-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(z,{className:"w-5 h-5 text-green-400"}),e.jsx("span",{className:"text-green-400 font-medium",children:"Success"})]}),e.jsx("p",{className:"text-green-300 mt-1",children:v}),e.jsx(o,{onClick:()=>n(null),variant:"secondary",size:"sm",className:"mt-2",children:"Dismiss"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[e.jsx("div",{className:"lg:col-span-1",children:e.jsx("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:e.jsx("nav",{className:"space-y-2",children:he.map(t=>{const w=t.icon,b=a===t.id;return e.jsxs("button",{onClick:()=>C(t.id),className:`
                        w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left
                        transition-all duration-200
                        ${b?"bg-primary-500/20 text-primary-400 border border-primary-500/30":"text-gray-300 hover:bg-background-tertiary hover:text-white"}
                      `,children:[e.jsx(w,{className:"w-5 h-5"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("span",{className:"font-medium block",children:t.label}),e.jsx("span",{className:"text-xs text-gray-500 block truncate",children:t.description})]})]},t.id)})})})}),e.jsx("div",{className:"lg:col-span-3",children:e.jsx(O.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.3},className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:K()},a)})]})]})})};export{ge as SettingsPage};
